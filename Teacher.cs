using System;

namespace SchoolManagement
{
    public class Teacher
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string RegistrationNumber { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Stage { get; set; } = string.Empty;
        public int ClassHours { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string Supervisor { get; set; } = string.Empty; // مسؤول المتابعة

        public Teacher()
        {
        }

        public Teacher(string name, string registrationNumber, string subject, string stage, int classHours, string phoneNumber, string supervisor = "")
        {
            Name = name;
            RegistrationNumber = registrationNumber;
            Subject = subject;
            Stage = stage;
            ClassHours = classHours;
            PhoneNumber = phoneNumber;
            Supervisor = supervisor;
        }

        public override string ToString()
        {
            return $"{Name} - {Subject} - {Stage}";
        }
    }
}
