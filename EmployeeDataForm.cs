using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class EmployeeDataForm : Form
    {
        // مسار ملف حفظ بيانات الموظفين
        private readonly string employeesFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Employees.txt");

        // Controls
        private TextBox employeeNameTextBox;
        private TextBox civilIdTextBox;
        private ComboBox subjectComboBox;
        private ComboBox workComboBox;
        private ComboBox stageComboBox;
        private ComboBox classesCountComboBox;
        private TextBox mobileTextBox;
        private ComboBox supervisorComboBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;
        private DataGridView employeesDataGridView;
        private List<Employee> employees;
        private Employee? editingEmployee;
        private bool isEditMode = false;

        public EmployeeDataForm()
        {
            employees = new List<Employee>();
            InitializeComponent();
            LoadEmployees();
            LoadComboBoxData();
        }

        public void LoadEmployeeForEdit(Employee employee)
        {
            editingEmployee = employee;
            isEditMode = true;

            // Load employee data into form fields
            employeeNameTextBox.Text = employee.Name;
            civilIdTextBox.Text = employee.CivilId;
            subjectComboBox.Text = employee.Subject;
            workComboBox.Text = employee.Work;
            stageComboBox.Text = employee.Stage;
            classesCountComboBox.Text = employee.ClassesCount;
            mobileTextBox.Text = employee.Mobile;
            supervisorComboBox.Text = employee.Supervisor;

            // Change save button text to update
            saveButton.Text = "تحديث";
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إدخال بيانات الموظفين";
            this.Size = new Size(650, 720);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إدخال بيانات الموظفين";
            titleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(550, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Left side - Input section
            CreateInputSection();

            // Right side - DataGridView
            CreateDataGridView();

            this.ResumeLayout(false);
        }

        private void CreateInputSection()
        {
            int yPosition = 70;
            int spacing = 40;

            // Employee Name
            Label nameLabel = new Label();
            nameLabel.Text = "اسم الموظف";
            nameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            nameLabel.Size = new Size(100, 25);
            nameLabel.Location = new Point(30, yPosition);
            this.Controls.Add(nameLabel);

            employeeNameTextBox = new TextBox();
            employeeNameTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            employeeNameTextBox.Size = new Size(250, 30);
            employeeNameTextBox.Location = new Point(30, yPosition + 25);
            this.Controls.Add(employeeNameTextBox);

            yPosition += spacing + 25;

            // Civil ID
            Label civilIdLabel = new Label();
            civilIdLabel.Text = "رقم السجل المدني (10 أرقام)";
            civilIdLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            civilIdLabel.Size = new Size(200, 25);
            civilIdLabel.Location = new Point(30, yPosition);
            this.Controls.Add(civilIdLabel);

            civilIdTextBox = new TextBox();
            civilIdTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            civilIdTextBox.Size = new Size(250, 30);
            civilIdTextBox.Location = new Point(30, yPosition + 25);
            civilIdTextBox.MaxLength = 10;
            civilIdTextBox.KeyPress += CivilIdTextBox_KeyPress;
            this.Controls.Add(civilIdTextBox);

            yPosition += spacing + 25;

            // Subject
            Label subjectLabel = new Label();
            subjectLabel.Text = "التخصص";
            subjectLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectLabel.Size = new Size(100, 25);
            subjectLabel.Location = new Point(30, yPosition);
            this.Controls.Add(subjectLabel);

            subjectComboBox = new ComboBox();
            subjectComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectComboBox.Size = new Size(250, 30);
            subjectComboBox.Location = new Point(30, yPosition + 25);
            subjectComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.Controls.Add(subjectComboBox);

            yPosition += spacing + 25;

            // Work
            Label workLabel = new Label();
            workLabel.Text = "العمل الحالي";
            workLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            workLabel.Size = new Size(100, 25);
            workLabel.Location = new Point(30, yPosition);
            this.Controls.Add(workLabel);

            workComboBox = new ComboBox();
            workComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            workComboBox.Size = new Size(250, 30);
            workComboBox.Location = new Point(30, yPosition + 25);
            workComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.Controls.Add(workComboBox);

            yPosition += spacing + 25;

            // Stage
            Label stageLabel = new Label();
            stageLabel.Text = "المرحلة";
            stageLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            stageLabel.Size = new Size(100, 25);
            stageLabel.Location = new Point(30, yPosition);
            this.Controls.Add(stageLabel);

            stageComboBox = new ComboBox();
            stageComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            stageComboBox.Size = new Size(250, 30);
            stageComboBox.Location = new Point(30, yPosition + 25);
            stageComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.Controls.Add(stageComboBox);

            yPosition += spacing + 25;

            // Classes Count
            Label classesLabel = new Label();
            classesLabel.Text = "عدد الحصص";
            classesLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            classesLabel.Size = new Size(100, 25);
            classesLabel.Location = new Point(30, yPosition);
            this.Controls.Add(classesLabel);

            classesCountComboBox = new ComboBox();
            classesCountComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            classesCountComboBox.Size = new Size(250, 30);
            classesCountComboBox.Location = new Point(30, yPosition + 25);
            classesCountComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            // Add "لا يدرس" and numbers 0-24
            classesCountComboBox.Items.Add("لا يدرس");
            for (int i = 0; i <= 24; i++)
            {
                classesCountComboBox.Items.Add(i.ToString());
            }
            this.Controls.Add(classesCountComboBox);

            yPosition += spacing + 25;

            // Mobile
            Label mobileLabel = new Label();
            mobileLabel.Text = "رقم الجوال (10 أرقام تبدأ بـ 05)";
            mobileLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            mobileLabel.Size = new Size(250, 25);
            mobileLabel.Location = new Point(30, yPosition);
            this.Controls.Add(mobileLabel);

            mobileTextBox = new TextBox();
            mobileTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            mobileTextBox.Size = new Size(250, 30);
            mobileTextBox.Location = new Point(30, yPosition + 25);
            mobileTextBox.MaxLength = 10;
            mobileTextBox.KeyPress += MobileTextBox_KeyPress;
            this.Controls.Add(mobileTextBox);

            yPosition += spacing + 25;

            // Supervisor
            Label supervisorLabel = new Label();
            supervisorLabel.Text = "مسؤول المتابعة";
            supervisorLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorLabel.Size = new Size(120, 25);
            supervisorLabel.Location = new Point(30, yPosition);
            this.Controls.Add(supervisorLabel);

            supervisorComboBox = new ComboBox();
            supervisorComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorComboBox.Size = new Size(250, 30);
            supervisorComboBox.Location = new Point(30, yPosition + 25);
            supervisorComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.Controls.Add(supervisorComboBox);

            // Buttons
            CreateButtons();
        }

        private void CreateButtons()
        {
            int buttonY = 580;
            int buttonRowSpacing = 50;

            // First row: Save and Edit buttons
            // Save Button
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(120, 35);
            saveButton.Location = new Point(30, buttonY);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // Edit Button
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(120, 35);
            editButton.Location = new Point(160, buttonY);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Second row: Delete and Close buttons
            // Delete Button
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(120, 35);
            deleteButton.Location = new Point(30, buttonY + buttonRowSpacing);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // Close Button
            closeButton = new Button();
            closeButton.Text = "إغلاق النافذة";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(120, 35);
            closeButton.Location = new Point(160, buttonY + buttonRowSpacing);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);
        }

        private void CreateDataGridView()
        {
            // DataGridView for employees
            employeesDataGridView = new DataGridView();
            employeesDataGridView.Location = new Point(320, 70);
            employeesDataGridView.Size = new Size(280, 600);
            employeesDataGridView.BackgroundColor = Color.White;
            employeesDataGridView.BorderStyle = BorderStyle.FixedSingle;
            employeesDataGridView.AllowUserToAddRows = false;
            employeesDataGridView.AllowUserToDeleteRows = false;
            employeesDataGridView.ReadOnly = true;
            employeesDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            employeesDataGridView.MultiSelect = false;
            employeesDataGridView.Font = new Font("Arial", 12, FontStyle.Bold);
            employeesDataGridView.RowHeadersVisible = false;
            employeesDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            
            // زيادة ارتفاع عنوان الجدول
            employeesDataGridView.ColumnHeadersHeight = 40;
            employeesDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

            // Add column
            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "EmployeeName";
            nameColumn.HeaderText = "اسم الموظف";
            nameColumn.DataPropertyName = "Name";
            nameColumn.DefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            employeesDataGridView.Columns.Add(nameColumn);

            // Set header font
            employeesDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);

            // Event handlers
            employeesDataGridView.SelectionChanged += EmployeesDataGridView_SelectionChanged;

            this.Controls.Add(employeesDataGridView);
        }

        private void LoadComboBoxData()
        {
            // Load subjects
            LoadSubjects();
            // Load works
            LoadWorks();
            // Load stages
            LoadStages();
            // Load supervisors
            LoadSupervisors();
        }

        private void LoadSubjects()
        {
            try
            {
                string subjectsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Subjects.txt");
                if (File.Exists(subjectsPath))
                {
                    var subjects = File.ReadAllLines(subjectsPath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                    subjectComboBox.Items.Clear();
                    subjectComboBox.Items.AddRange(subjects.ToArray());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل التخصصات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadWorks()
        {
            try
            {
                string worksPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Works.txt");
                if (File.Exists(worksPath))
                {
                    var works = File.ReadAllLines(worksPath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                    workComboBox.Items.Clear();
                    workComboBox.Items.AddRange(works.ToArray());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل الأعمال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadStages()
        {
            try
            {
                string stagesPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Stages.txt");
                if (File.Exists(stagesPath))
                {
                    var stages = File.ReadAllLines(stagesPath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                    stageComboBox.Items.Clear();
                    stageComboBox.Items.AddRange(stages.ToArray());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل المراحل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSupervisors()
        {
            try
            {
                string supervisorsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Supervisors.txt");
                if (File.Exists(supervisorsPath))
                {
                    var supervisors = File.ReadAllLines(supervisorsPath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                    supervisorComboBox.Items.Clear();
                    supervisorComboBox.Items.AddRange(supervisors.ToArray());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل مسؤولي المتابعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CivilIdTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Allow only digits and control characters
            if (!char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void MobileTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Allow only digits and control characters
            if (!char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                if (isEditMode && editingEmployee != null)
                {
                    // Update existing employee
                    UpdateEmployee();
                }
                else
                {
                    // Add new employee
                    AddNewEmployee();
                }
            }
        }

        private void AddNewEmployee()
        {
            var newEmployee = new Employee
            {
                Name = employeeNameTextBox.Text.Trim(),
                CivilId = civilIdTextBox.Text.Trim(),
                Subject = subjectComboBox.Text,
                Work = workComboBox.Text,
                Stage = stageComboBox.Text,
                ClassesCount = classesCountComboBox.Text,
                Mobile = mobileTextBox.Text.Trim(),
                Supervisor = supervisorComboBox.Text
            };

            // Check for duplicate civil ID
            if (employees.Any(emp => emp.CivilId == newEmployee.CivilId))
            {
                MessageBox.Show("رقم السجل المدني موجود مسبقاً!", "موظف مكرر",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            employees.Add(newEmployee);
            SaveEmployees();
            RefreshDataGridView();

            ClearInputs();

            // Update view form if open
            ViewEmployeeDataForm.Instance?.LoadEmployees();

            // Add automatic distribution for new teacher
            AutoDistributionForm.AddNewTeacherDistribution(employeeNameTextBox.Text.Trim(), supervisorComboBox.Text);

            MessageBox.Show("تم حفظ بيانات الموظف بنجاح!", "تأكيد الحفظ",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UpdateEmployee()
        {
            if (editingEmployee == null) return;

            string newCivilId = civilIdTextBox.Text.Trim();

            // Check for duplicate civil ID (excluding current employee)
            if (newCivilId != editingEmployee.CivilId && employees.Any(emp => emp.CivilId == newCivilId))
            {
                MessageBox.Show("رقم السجل المدني موجود مسبقاً!", "موظف مكرر",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Update employee data
            editingEmployee.Name = employeeNameTextBox.Text.Trim();
            editingEmployee.CivilId = newCivilId;
            editingEmployee.Mobile = mobileTextBox.Text.Trim();
            editingEmployee.Subject = subjectComboBox.Text;
            editingEmployee.Work = workComboBox.Text;
            editingEmployee.Stage = stageComboBox.Text;
            editingEmployee.ClassesCount = classesCountComboBox.Text;
            editingEmployee.Supervisor = supervisorComboBox.Text;

            SaveEmployees();
            ClearInputs();
            editingEmployee = null;
            saveButton.Text = "حفظ";

            // Update view form if open
            ViewEmployeeDataForm.Instance?.LoadEmployees();

            // Update distribution form if open
            AutoDistributionForm.UpdateTeacherDistribution(editingEmployee.Name, employeeNameTextBox.Text.Trim(), supervisorComboBox.Text);

            MessageBox.Show("تم تحديث بيانات الموظف بنجاح!", "تأكيد التحديث",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (employeesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار موظف للتعديل", "لم يتم اختيار موظف",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            int selectedIndex = employeesDataGridView.SelectedRows[0].Index;
            string originalCivilId = employees[selectedIndex].CivilId;
            string newCivilId = civilIdTextBox.Text.Trim();

            // Check for duplicate civil ID (excluding current employee)
            if (newCivilId != originalCivilId && employees.Any(emp => emp.CivilId == newCivilId))
            {
                MessageBox.Show("رقم السجل المدني موجود مسبقاً!", "موظف مكرر",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            employees[selectedIndex].Name = employeeNameTextBox.Text.Trim();
            employees[selectedIndex].CivilId = newCivilId;
            employees[selectedIndex].Subject = subjectComboBox.Text;
            employees[selectedIndex].Work = workComboBox.Text;
            employees[selectedIndex].Stage = stageComboBox.Text;
            employees[selectedIndex].ClassesCount = classesCountComboBox.Text;
            employees[selectedIndex].Mobile = mobileTextBox.Text.Trim();
            employees[selectedIndex].Supervisor = supervisorComboBox.Text;

            SaveEmployees();
            RefreshDataGridView();
            ClearInputs();

            MessageBox.Show("تم تعديل بيانات الموظف بنجاح!", "تأكيد التعديل",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (employeesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار موظف للحذف", "لم يتم اختيار موظف",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = employeesDataGridView.SelectedRows[0].Index;
            string selectedEmployee = employees[selectedIndex].Name;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الموظف '{selectedEmployee}'؟",
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                // Remove from distribution before removing from employees list
                AutoDistributionForm.RemoveTeacherDistribution(selectedEmployee);

                employees.RemoveAt(selectedIndex);
                SaveEmployees();
                RefreshDataGridView();
                ClearInputs();

                // Update view form if open
                ViewEmployeeDataForm.Instance?.LoadEmployees();

                MessageBox.Show("تم حذف الموظف بنجاح!", "تأكيد الحذف",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من الخروج من نافذة إدخال بيانات الموظفين؟",
                                       "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                this.Close();
            }
        }

        private void EmployeesDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (employeesDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = employeesDataGridView.SelectedRows[0].Index;
                if (selectedIndex < employees.Count)
                {
                    var employee = employees[selectedIndex];
                    employeeNameTextBox.Text = employee.Name;
                    civilIdTextBox.Text = employee.CivilId;
                    subjectComboBox.Text = employee.Subject;
                    workComboBox.Text = employee.Work;
                    stageComboBox.Text = employee.Stage;
                    classesCountComboBox.Text = employee.ClassesCount;
                    mobileTextBox.Text = employee.Mobile;
                    supervisorComboBox.Text = employee.Supervisor;
                }
            }
        }

        private bool ValidateInput()
        {
            // Validate employee name
            if (string.IsNullOrWhiteSpace(employeeNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الموظف", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                employeeNameTextBox.Focus();
                return false;
            }

            // Validate civil ID
            if (string.IsNullOrWhiteSpace(civilIdTextBox.Text) || civilIdTextBox.Text.Length != 10)
            {
                MessageBox.Show("يرجى إدخال رقم السجل المدني (10 أرقام بالضبط)", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                civilIdTextBox.Focus();
                return false;
            }

            // Validate subject
            if (subjectComboBox.SelectedIndex == -1 || string.IsNullOrWhiteSpace(subjectComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار التخصص", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                subjectComboBox.Focus();
                return false;
            }

            // Validate work
            if (workComboBox.SelectedIndex == -1 || string.IsNullOrWhiteSpace(workComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار العمل الحالي", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                workComboBox.Focus();
                return false;
            }

            // Validate stage
            if (stageComboBox.SelectedIndex == -1 || string.IsNullOrWhiteSpace(stageComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار المرحلة", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                stageComboBox.Focus();
                return false;
            }

            // Validate classes count
            if (classesCountComboBox.SelectedIndex == -1 || string.IsNullOrWhiteSpace(classesCountComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار عدد الحصص", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                classesCountComboBox.Focus();
                return false;
            }

            // Validate mobile number (optional but if entered must be valid)
            if (!string.IsNullOrWhiteSpace(mobileTextBox.Text))
            {
                if (mobileTextBox.Text.Length != 10 || !mobileTextBox.Text.StartsWith("05"))
                {
                    MessageBox.Show("يرجى إدخال رقم جوال صحيح (10 أرقام تبدأ بـ 05)", "خطأ في البيانات",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    mobileTextBox.Focus();
                    return false;
                }
            }

            // Validate supervisor
            if (supervisorComboBox.SelectedIndex == -1 || string.IsNullOrWhiteSpace(supervisorComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار مسؤول المتابعة", "حقل مطلوب",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                supervisorComboBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearInputs()
        {
            employeeNameTextBox.Clear();
            civilIdTextBox.Clear();
            subjectComboBox.SelectedIndex = -1;
            workComboBox.SelectedIndex = -1;
            stageComboBox.SelectedIndex = -1;
            classesCountComboBox.SelectedIndex = -1;
            mobileTextBox.Clear();
            supervisorComboBox.SelectedIndex = -1;
        }

        private void SaveEmployees()
        {
            try
            {
                string directory = Path.GetDirectoryName(employeesFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var lines = employees.Select(emp =>
                    $"{emp.Name}|{emp.CivilId}|{emp.Subject}|{emp.Work}|{emp.Stage}|{emp.ClassesCount}|{emp.Mobile}|{emp.Supervisor}");
                File.WriteAllLines(employeesFilePath, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ بيانات الموظفين: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadEmployees()
        {
            try
            {
                if (File.Exists(employeesFilePath))
                {
                    var lines = File.ReadAllLines(employeesFilePath).Where(s => !string.IsNullOrWhiteSpace(s));
                    employees.Clear();

                    foreach (var line in lines)
                    {
                        var parts = line.Split('|');
                        if (parts.Length == 8)
                        {
                            employees.Add(new Employee
                            {
                                Name = parts[0],
                                CivilId = parts[1],
                                Subject = parts[2],
                                Work = parts[3],
                                Stage = parts[4],
                                ClassesCount = parts[5],
                                Mobile = parts[6],
                                Supervisor = parts[7]
                            });
                        }
                    }
                }
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            employeesDataGridView.DataSource = null;
            employeesDataGridView.DataSource = employees.Select(emp => new { Name = emp.Name }).ToList();
        }
    }

    // Employee class to hold employee data
    public class Employee
    {
        public string Name { get; set; } = "";
        public string CivilId { get; set; } = "";
        public string Subject { get; set; } = "";
        public string Work { get; set; } = "";
        public string Stage { get; set; } = "";
        public string ClassesCount { get; set; } = "";
        public string Mobile { get; set; } = "";
        public string Supervisor { get; set; } = "";
    }
}
