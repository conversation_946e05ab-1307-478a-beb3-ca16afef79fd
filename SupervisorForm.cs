using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class SupervisorForm : Form
    {
        private TextBox supervisorTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button exitButton;
        private DataGridView supervisorsDataGridView;
        private List<string> supervisors;

        private readonly string supervisorsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Supervisors.txt");

        public SupervisorForm()
        {
            supervisors = new List<string>();
            InitializeComponent();
            LoadSupervisors();
            RefreshDataGridView();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "إدارة المشرفين";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إدارة المشرفين";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87);
            titleLabel.Size = new Size(500, 40);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // Input Section
            Label inputLabel = new Label();
            inputLabel.Text = "اسم المشرف:";
            inputLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            inputLabel.Size = new Size(100, 25);
            inputLabel.Location = new Point(50, 80);
            this.Controls.Add(inputLabel);

            supervisorTextBox = new TextBox();
            supervisorTextBox.Font = new Font("Arial", 12);
            supervisorTextBox.Size = new Size(200, 25);
            supervisorTextBox.Location = new Point(50, 110);
            this.Controls.Add(supervisorTextBox);

            // Buttons
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(80, 35);
            saveButton.Location = new Point(50, 150);
            saveButton.BackColor = Color.FromArgb(40, 167, 69);
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(80, 35);
            editButton.Location = new Point(50, 200);
            editButton.BackColor = Color.FromArgb(255, 193, 7);
            editButton.ForeColor = Color.Black;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(80, 35);
            deleteButton.Location = new Point(50, 250);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69);
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            exitButton = new Button();
            exitButton.Text = "خروج";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(80, 35);
            exitButton.Location = new Point(50, 300);
            exitButton.BackColor = Color.FromArgb(108, 117, 125);
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            // DataGridView
            supervisorsDataGridView = new DataGridView();
            supervisorsDataGridView.Location = new Point(300, 80);
            supervisorsDataGridView.Size = new Size(250, 350);
            supervisorsDataGridView.Font = new Font("Arial", 12);
            supervisorsDataGridView.BackgroundColor = Color.White;
            supervisorsDataGridView.BorderStyle = BorderStyle.FixedSingle;
            supervisorsDataGridView.AllowUserToAddRows = false;
            supervisorsDataGridView.AllowUserToDeleteRows = false;
            supervisorsDataGridView.ReadOnly = true;
            supervisorsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            supervisorsDataGridView.MultiSelect = false;
            supervisorsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            supervisorsDataGridView.SelectionChanged += SupervisorsDataGridView_SelectionChanged;

            // Add column
            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "Name";
            nameColumn.HeaderText = "اسم المشرف";
            nameColumn.Width = 200;
            supervisorsDataGridView.Columns.Add(nameColumn);

            this.Controls.Add(supervisorsDataGridView);

            this.ResumeLayout(false);
        }

        private void LoadSupervisors()
        {
            try
            {
                if (File.Exists(supervisorsFilePath))
                {
                    supervisors = File.ReadAllLines(supervisorsFilePath).ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المشرفين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveSupervisors()
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(supervisorsFilePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                File.WriteAllLines(supervisorsFilePath, supervisors);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المشرفين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            supervisorsDataGridView.Rows.Clear();
            foreach (string supervisor in supervisors)
            {
                supervisorsDataGridView.Rows.Add(supervisor);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(supervisorTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المشرف!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (supervisors.Contains(supervisorTextBox.Text.Trim()))
            {
                MessageBox.Show("هذا المشرف موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            supervisors.Add(supervisorTextBox.Text.Trim());
            SaveSupervisors();
            RefreshDataGridView();
            supervisorTextBox.Clear();
            MessageBox.Show("تم حفظ المشرف بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (supervisorsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مشرف للتعديل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(supervisorTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الجديد للمشرف!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = supervisorsDataGridView.SelectedRows[0].Index;
            string oldName = supervisors[selectedIndex];
            string newName = supervisorTextBox.Text.Trim();

            if (supervisors.Contains(newName) && newName != oldName)
            {
                MessageBox.Show("هذا المشرف موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            supervisors[selectedIndex] = newName;
            SaveSupervisors();
            RefreshDataGridView();
            supervisorTextBox.Clear();
            MessageBox.Show("تم تعديل المشرف بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (supervisorsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مشرف للحذف!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المشرف؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                int selectedIndex = supervisorsDataGridView.SelectedRows[0].Index;
                supervisors.RemoveAt(selectedIndex);
                SaveSupervisors();
                RefreshDataGridView();
                supervisorTextBox.Clear();
                MessageBox.Show("تم حذف المشرف بنجاح!", "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void SupervisorsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (supervisorsDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = supervisorsDataGridView.SelectedRows[0].Index;
                if (selectedIndex < supervisors.Count)
                {
                    supervisorTextBox.Text = supervisors[selectedIndex];
                }
            }
        }
    }
}
