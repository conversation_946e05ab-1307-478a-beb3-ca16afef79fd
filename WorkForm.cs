using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class WorkForm : Form
    {
        private TextBox workTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button exitButton;
        private DataGridView worksDataGridView;
        private List<string> works;

        private readonly string worksFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Works.txt");

        public WorkForm()
        {
            works = new List<string>();
            InitializeComponent();
            LoadWorks();
            RefreshDataGridView();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "إدارة الأعمال";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إدارة الأعمال";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87);
            titleLabel.Size = new Size(500, 40);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // Input Section
            Label inputLabel = new Label();
            inputLabel.Text = "اسم العمل:";
            inputLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            inputLabel.Size = new Size(100, 25);
            inputLabel.Location = new Point(50, 80);
            this.Controls.Add(inputLabel);

            workTextBox = new TextBox();
            workTextBox.Font = new Font("Arial", 12);
            workTextBox.Size = new Size(200, 25);
            workTextBox.Location = new Point(50, 110);
            this.Controls.Add(workTextBox);

            // Buttons
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(80, 35);
            saveButton.Location = new Point(50, 150);
            saveButton.BackColor = Color.FromArgb(40, 167, 69);
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(80, 35);
            editButton.Location = new Point(50, 200);
            editButton.BackColor = Color.FromArgb(255, 193, 7);
            editButton.ForeColor = Color.Black;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(80, 35);
            deleteButton.Location = new Point(50, 250);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69);
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            exitButton = new Button();
            exitButton.Text = "خروج";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(80, 35);
            exitButton.Location = new Point(50, 300);
            exitButton.BackColor = Color.FromArgb(108, 117, 125);
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            // DataGridView
            worksDataGridView = new DataGridView();
            worksDataGridView.Location = new Point(300, 80);
            worksDataGridView.Size = new Size(250, 350);
            worksDataGridView.Font = new Font("Arial", 12);
            worksDataGridView.BackgroundColor = Color.White;
            worksDataGridView.BorderStyle = BorderStyle.FixedSingle;
            worksDataGridView.AllowUserToAddRows = false;
            worksDataGridView.AllowUserToDeleteRows = false;
            worksDataGridView.ReadOnly = true;
            worksDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            worksDataGridView.MultiSelect = false;
            worksDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            worksDataGridView.SelectionChanged += WorksDataGridView_SelectionChanged;

            // Add column
            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "Name";
            nameColumn.HeaderText = "اسم العمل";
            nameColumn.Width = 200;
            worksDataGridView.Columns.Add(nameColumn);

            this.Controls.Add(worksDataGridView);

            this.ResumeLayout(false);
        }

        private void LoadWorks()
        {
            try
            {
                if (File.Exists(worksFilePath))
                {
                    works = File.ReadAllLines(worksFilePath).ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأعمال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveWorks()
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(worksFilePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                File.WriteAllLines(worksFilePath, works);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الأعمال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            worksDataGridView.Rows.Clear();
            foreach (string work in works)
            {
                worksDataGridView.Rows.Add(work);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(workTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العمل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (works.Contains(workTextBox.Text.Trim()))
            {
                MessageBox.Show("هذا العمل موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            works.Add(workTextBox.Text.Trim());
            SaveWorks();
            RefreshDataGridView();
            workTextBox.Clear();
            MessageBox.Show("تم حفظ العمل بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (worksDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عمل للتعديل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(workTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الجديد للعمل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = worksDataGridView.SelectedRows[0].Index;
            string oldName = works[selectedIndex];
            string newName = workTextBox.Text.Trim();

            if (works.Contains(newName) && newName != oldName)
            {
                MessageBox.Show("هذا العمل موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            works[selectedIndex] = newName;
            SaveWorks();
            RefreshDataGridView();
            workTextBox.Clear();
            MessageBox.Show("تم تعديل العمل بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (worksDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عمل للحذف!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا العمل؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                int selectedIndex = worksDataGridView.SelectedRows[0].Index;
                works.RemoveAt(selectedIndex);
                SaveWorks();
                RefreshDataGridView();
                workTextBox.Clear();
                MessageBox.Show("تم حذف العمل بنجاح!", "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void WorksDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (worksDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = worksDataGridView.SelectedRows[0].Index;
                if (selectedIndex < works.Count)
                {
                    workTextBox.Text = works[selectedIndex];
                }
            }
        }
    }
}
