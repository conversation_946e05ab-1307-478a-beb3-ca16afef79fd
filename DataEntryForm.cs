using System;
using System.Drawing;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class DataEntryForm : Form
    {
        public DataEntryForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إدخال البيانات";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إدخال البيانات";
            titleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(400, 30);
            titleLabel.Location = new Point(50, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Employee Data Entry Button
            Button employeeDataButton = new Button();
            employeeDataButton.Text = "إدخال بيانات الموظفين";
            employeeDataButton.Font = new Font("Arial", 12, FontStyle.Bold);
            employeeDataButton.Size = new Size(300, 50);
            employeeDataButton.Location = new Point(100, 80);
            employeeDataButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            employeeDataButton.ForeColor = Color.White;
            employeeDataButton.FlatStyle = FlatStyle.Flat;
            employeeDataButton.FlatAppearance.BorderSize = 0;
            employeeDataButton.Cursor = Cursors.Hand;
            employeeDataButton.Click += EmployeeDataButton_Click;
            this.Controls.Add(employeeDataButton);

            // Daily Absence Entry Button
            Button dailyAbsenceButton = new Button();
            dailyAbsenceButton.Text = "إدخال الغياب اليومي";
            dailyAbsenceButton.Font = new Font("Arial", 12, FontStyle.Bold);
            dailyAbsenceButton.Size = new Size(300, 50);
            dailyAbsenceButton.Location = new Point(100, 150);
            dailyAbsenceButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning yellow
            dailyAbsenceButton.ForeColor = Color.Black;
            dailyAbsenceButton.FlatStyle = FlatStyle.Flat;
            dailyAbsenceButton.FlatAppearance.BorderSize = 0;
            dailyAbsenceButton.Cursor = Cursors.Hand;
            dailyAbsenceButton.Click += DailyAbsenceButton_Click;
            this.Controls.Add(dailyAbsenceButton);

            // Notes Entry Button
            Button notesButton = new Button();
            notesButton.Text = "إدخال الملاحظات";
            notesButton.Font = new Font("Arial", 12, FontStyle.Bold);
            notesButton.Size = new Size(300, 50);
            notesButton.Location = new Point(100, 220);
            notesButton.BackColor = Color.FromArgb(23, 162, 184); // Bootstrap info cyan
            notesButton.ForeColor = Color.White;
            notesButton.FlatStyle = FlatStyle.Flat;
            notesButton.FlatAppearance.BorderSize = 0;
            notesButton.Cursor = Cursors.Hand;
            notesButton.Click += NotesButton_Click;
            this.Controls.Add(notesButton);

            // Close Window Button
            Button closeButton = new Button();
            closeButton.Text = "إغلاق النافذة";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(300, 50);
            closeButton.Location = new Point(100, 290);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);

            this.ResumeLayout(false);
        }

        private void EmployeeDataButton_Click(object sender, EventArgs e)
        {
            EmployeeDataForm employeeDataForm = new EmployeeDataForm();
            employeeDataForm.ShowDialog();
        }

        private void DailyAbsenceButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم إضافة نافذة إدخال الغياب اليومي قريباً", "قيد التطوير", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void NotesButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم إضافة نافذة إدخال الملاحظات قريباً", "قيد التطوير", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
