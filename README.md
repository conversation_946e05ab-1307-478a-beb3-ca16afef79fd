# منظم أعمال إدارة المدرسة

## وصف البرنامج
برنامج منظم أعمال إدارة المدرسة هو تطبيق Windows Forms مكتوب بلغة C# لإدارة بيانات المعلمين في المدرسة.

## المميزات

### النافذة الرئيسية
- **عنوان محسن:** "منظم أعمال إدارة المدرسة" مع تصميم عصري
- **زر إدارة بيانات المعلمين:** للانتقال إلى نافذة إدارة المعلمين (أزرق Bootstrap)
- **زر التوزيع الآلي الجديد:** لوظيفة التوزيع الآلي المستقبلية (أخضر Bootstrap)
- **زر الخروج:** لإغلاق التطبيق (أحمر Bootstrap)
- **تصميم حديث:** ألوان Bootstrap مع تأثيرات تفاعلية وخلفية محسنة

### نافذة إدارة بيانات المعلمين
- **إدخال البيانات (تخطيط محسن):**
  - **عناوين الحقول:** فوق حقول الإدخال مباشرة ومتوسطة أفقياً
  - حقل الاسم (نص) - الأول من اليمين
  - حقل رقم السجل (نص) - الثاني من اليمين
  - حقل التخصص (قائمة منسدلة) - الثالث من اليمين: دراسات إسلامية، لغة عربية، دراسات اجتماعية، رياضيات، علوم، لغة إنجليزية، تربية بدنية، تربية فنية، مهارات حياتية، تقنية رقمية
  - حقل المرحلة (قائمة منسدلة) - الرابع من اليمين: ابتدائي، متوسط، ثانوي، تربية خاصة
  - حقل عدد الحصص (قائمة منسدلة) - الخامس من اليمين: من 1 إلى 24
  - حقل رقم الجوال (10 أرقام بالضبط) - السادس من اليمين
  - الحقول منظمة في صف أفقي واحد داخل مجموعة "بيانات المعلم"

- **العمليات:**
  - حفظ بيانات معلم جديد
  - تعديل بيانات معلم موجود
  - حذف معلم مع رسالة تأكيد
  - مسح الحقول
  - إنشاء تقرير شامل لبيانات المعلمين
  - **الترتيب الصحيح من اليمين:** حفظ → تعديل → حذف → مسح الحقول → تقرير
  - الأزرار متساوية في الحجم (100 بكسل عرض) ومتساوية في المسافات (20 بكسل)
  - **توسيط مثالي:** الأزرار متوسطة تماماً أفقياً وعمودياً في مجموعة "العمليات"
  - **مسافات متساوية:** المسافة من الأزرار إلى حد المجموعة من الأعلى والأسفل متساوية

- **عرض البيانات:**
  - جدول يعرض جميع بيانات المعلمين (الرقم، الاسم، رقم السجل، التخصص، المرحلة، عدد الحصص، رقم الجوال)
  - توزيع مناسب لأحجام الأعمدة
  - إمكانية تحديد صف للتعديل أو الحذف

- **البحث والتصفية:**
  - البحث بالاسم
  - البحث بالتخصص
  - البحث بالمرحلة
  - زر لمسح البحث
  - عرض النتائج المفلترة في الجدول

- **الإحصائيات:**
  - عداد يظهر عدد المعلمين المسجلين
  - يتحدث تلقائياً عند الإضافة أو الحذف

- **التقارير:**
  - تقرير شامل يعرض جميع بيانات المعلمين المعروضة حالياً في الجدول
  - إحصائيات عامة: إجمالي عدد المعلمين
  - إحصائيات حسب التخصص: توزيع المعلمين على التخصصات المختلفة
  - إحصائيات حسب المرحلة: توزيع المعلمين على المراحل التعليمية
  - إحصائيات الحصص: إجمالي ومتوسط عدد الحصص
  - تفاصيل كاملة لجميع المعلمين في جدول منسق
  - إمكانية طباعة التقرير
  - إمكانية حفظ التقرير كملف نصي
  - واجهة تقرير منفصلة قابلة لتغيير الحجم

## التحقق من صحة البيانات
- التأكد من إدخال جميع الحقول المطلوبة
- التحقق من أن رقم الجوال يتكون من 10 أرقام بالضبط
- التحقق من عدم تكرار رقم السجل
- التحقق من عدم تكرار رقم الجوال
- عرض رسائل خطأ واضحة

## رسائل التأكيد
- رسالة تأكيد عند حفظ بيانات معلم جديد
- رسالة تأكيد عند تعديل بيانات معلم
- رسالة تأكيد عند حذف معلم
- رسالة تأكيد قبل الحذف

## كيفية تشغيل البرنامج

### المتطلبات
- .NET 6.0 أو أحدث
- Windows OS

### التشغيل
```bash
# بناء المشروع
dotnet build SchoolManagement.csproj

# تشغيل البرنامج
dotnet run --project SchoolManagement.csproj
```

## بنية المشروع
- `Program.cs` - نقطة دخول التطبيق
- `MainForm.cs` - النافذة الرئيسية
- `TeachersForm.cs` - نافذة إدارة بيانات المعلمين
- `AutoDistributionForm.cs` - نافذة التوزيع الآلي للمهام
- `ReportForm.cs` - نافذة عرض التقارير
- `Teacher.cs` - نموذج بيانات المعلم
- `SchoolManagement.csproj` - ملف المشروع

## البيانات التجريبية
يحتوي البرنامج على بيانات تجريبية لثلاثة معلمين لتسهيل اختبار الوظائف:
- أحمد محمد (T001) - رياضيات - ابتدائي - 18 حصة
- فاطمة علي (T002) - لغة عربية - متوسط - 20 حصة
- محمد سالم (T003) - علوم - ثانوي - 16 حصة

## الواجهة
- واجهة باللغة العربية مع دعم الكتابة من اليمين إلى اليسار
- **تصميم حديث يشبه CSS:** تنسيق احترافي مستوحى من تقنيات الويب الحديثة
- **مجموعات منظمة:** حقول الإدخال والأزرار في مجموعات منفصلة مع خلفيات مميزة
- حقول الإدخال في صف أفقي واحد مرتبة من اليمين إلى اليسار: (الاسم، رقم السجل، التخصص، المرحلة، عدد الحصص، رقم الجوال)
- **الأزرار متوسطة تماماً** في مجموعة العمليات مع تباعد متساوي
- **ألوان Bootstrap:** استخدام نظام ألوان Bootstrap للأزرار (أخضر للحفظ، أزرق للتعديل، أحمر للحذف، إلخ)
- **تأثيرات تفاعلية:** تغيير لون الأزرار عند التمرير عليها (hover effects)
- جدول محسن مع تنسيق CSS-like وألوان متدرجة
- عرض شاشة مُحسن ومضغوط (1000 بكسل) لسهولة الاستخدام
- خط موحد Arial بحجم 12 Bold في جميع أجزاء النافذة لوضوح أفضل
- **نظام ألوان متسق:** استخدام لوحة ألوان موحدة عبر التطبيق

## التصميم المحسن (CSS-like)
- **لوحة الألوان:**
  - خلفية التطبيق: `#F8F9FA` (رمادي فاتح)
  - نص رئيسي: `#495057` (رمادي داكن)
  - حدود: `#DEE2E6` (رمادي فاتح للحدود)

- **ألوان الأزرار (Bootstrap Colors):**
  - حفظ: `#28A745` (أخضر النجاح)
  - تعديل: `#007BFF` (أزرق أساسي)
  - حذف: `#DC3545` (أحمر الخطر)
  - مسح الحقول: `#FFC107` (أصفر التحذير)
  - تقرير: `#6C757D` (رمادي ثانوي)

- **تأثيرات تفاعلية:**
  - تغيير لون الأزرار عند التمرير
  - مؤشر اليد عند التمرير على الأزرار
  - حدود مسطحة للعناصر

- **تخطيط متجاوب:**
  - توسيط الأزرار تلقائياً في المجموعة
  - حسابات CSS-like للمسافات والتوسيط
  - تنسيق الجدول مع ألوان متدرجة

## تحسينات التخطيط الجديدة
- **عناوين الحقول المحسنة:**
  - العناوين فوق الحقول مباشرة بدلاً من جانبها
  - توسيط العناوين أفقياً فوق كل حقل
  - إزالة النقطتين من العناوين لمظهر أنظف
  - تباعد مثالي بين العناوين والحقول

- **توسيط الأزرار المثالي:**
  - حسابات رياضية دقيقة للتوسيط الأفقي والعمودي
  - المسافة من الأزرار إلى حدود المجموعة متساوية من جميع الجهات
  - توزيع متساوي للأزرار داخل المجموعة
  - مسافات محسوبة بدقة لمظهر احترافي

## الميزات الجديدة
### زر التوزيع الآلي
- **الموقع:** في الشاشة الرئيسية بين زر إدارة المعلمين وزر الخروج
- **التصميم:** أخضر Bootstrap مع تأثيرات تفاعلية
- **الوظيفة الحالية:** رسالة إعلامية تشير إلى أن الوظيفة ستكون متاحة قريباً
- **الهدف المستقبلي:** توزيع المعلمين على الفصول والمواد تلقائياً
- **التكامل:** مصمم ليتكامل مع نظام إدارة المعلمين الحالي

### نافذة التوزيع الآلي للمهام
- **مجموعة العمليات المحسنة (4 أزرار):**
  - **التوزيع الآلي:** توزيع المهام تلقائياً على المعلمين (أخضر Bootstrap)
  - **تفريغ جميع الحقول:** مسح جميع التكليفات الحالية (أصفر Bootstrap)
  - **إعادة التوزيع الآلي:** إعادة توزيع المهام مع استبدال التوزيع الحالي (أزرق Bootstrap)
  - **تقرير:** إنشاء تقرير بتصميم CSS حديث لبيانات الجدول المعروضة (رمادي Bootstrap)
  - الأزرار متوسطة في صف أفقي واحد مع ألوان Bootstrap

- **قسم البحث والتصفية الشامل:**
  - **تخطيط صف واحد:** جميع عناصر البحث في صف أفقي واحد متناسق
  - **الترتيب المحسن من اليمين:** البحث بالاسم → مسؤول المتابعة → تاريخ معين → من تاريخ → إلى تاريخ → مستوى التنفيذ → زر بحث → زر مسح البحث
  - **البحث بمستوى التنفيذ:** خيارات "الكل"، "نعم"، "لا"، "غير محدد"
  - **تصميم مضغوط:** ارتفاع مجموعة البحث 80 بكسل مع مسافة 120 بكسل بين العناصر
  - **خطوط مصغرة:** حجم خط 9 للعناوين والحقول لتوفير المساحة

- **جدول التوزيع المحسن والقابل للتعديل:**
  - **أحجام أعمدة محسنة لملء الجدول:**
    - اسم المعلم: 200 بكسل (موسع لعرض أفضل للأسماء) - غير قابل للتعديل
    - تحضير1/2، واجبات1/2، زيارة1/2: 110 بكسل لكل عمود (قابل للتعديل)
    - مسؤول المتابعة: 190 بكسل (موسع لملء المساحة المتبقية) - قائمة منسدلة
    - مستوى التنفيذ: 100 بكسل - خيارات "نعم" أو "لا"
  - **تخطيط محسن:** ارتفاع الجدول 390 بكسل مع موقع محسن
  - **توزيع متوازن:** أحجام الأعمدة موزعة بشكل مثالي لاستغلال المساحة

- **ميزات التعديل المباشر:**
  - **تعديل بالنقر:** النقر على أي حقل قابل للتعديل لبدء التعديل
  - **حفظ تلقائي:** الحفظ التلقائي عند مغادرة الحقل
  - **مؤشر بصري:** تغيير لون الحقل إلى أخضر فاتح عند الحفظ
  - **التحقق من صحة البيانات:** التحقق من صحة التواريخ المدخلة
  - **قوائم منسدلة:** للمسؤولين ومستوى التنفيذ
  - **اختيار واحد فقط:** في مستوى التنفيذ (نعم أو لا)

- **الميزات العامة:**
  - تصميم CSS حديث مع ألوان Bootstrap
  - بيانات تجريبية للاختبار
  - تصفية ديناميكية للبيانات
  - عداد السجلات المعروضة
  - رسائل تأكيد للعمليات الحساسة

## ميزة التقرير المحسن بتصميم CSS
### تصميم احترافي يشبه تقارير CSS الحديثة
- **رأس التقرير الأنيق:**
  - إطار مزخرف بأحرف Unicode (╔═══╗)
  - عنوان مركزي واضح
  - تاريخ ووقت الإنشاء

- **جدول بيانات بتصميم CSS:**
  - حدود احترافية باستخدام أحرف Unicode
  - رأس جدول واضح مع فواصل
  - صفوف منظمة مع حدود بين كل صف
  - عرض "-" للحقول الفارغة لوضوح أفضل

- **تذييل التقرير:**
  - إطار ملخص أنيق
  - عدد السجلات المعروضة
  - تاريخ ووقت الإنشاء التفصيلي

- **ميزات التقرير:**
  - يعرض البيانات المفلترة فقط (حسب البحث الحالي)
  - تنسيق احترافي قابل للطباعة
  - حفظ كملف نصي مع تنسيق محفوظ
  - سهولة القراءة والفهم

## تحسينات التخطيط الجديدة في نافذة التوزيع الآلي
### إعادة تنظيم قسم البحث
- **صف واحد متناسق:** جميع عناصر البحث في صف أفقي واحد بدلاً من صفين
- **ترتيب منطقي من اليمين:**
  1. البحث بالاسم (120 بكسل)
  2. مسؤول المتابعة (120 بكسل)
  3. تاريخ معين (120 بكسل)
  4. من تاريخ (120 بكسل)
  5. إلى تاريخ (120 بكسل)
  6. زر بحث (60 بكسل)
  7. زر مسح البحث (65 بكسل)
- **مساحة مضغوطة:** تقليل ارتفاع مجموعة البحث من 120 إلى 80 بكسل
- **خطوط مصغرة:** استخدام خط بحجم 9 لتوفير المساحة

### تحسين أحجام أعمدة الجدول لملء المساحة
- **استغلال كامل للعرض:** إعادة توزيع أحجام الأعمدة لملء عرض الجدول بالكامل
- **توسيع الأعمدة الرئيسية:**
  - اسم المعلم: من 160 إلى 200 بكسل (لعرض أفضل للأسماء الطويلة)
  - مسؤول المتابعة: من 130 إلى 190 بكسل (لملء المساحة المتبقية)
  - مستوى التنفيذ: يبقى 100 بكسل (مناسب للخيارات المحدودة)
- **توزيع مثالي:** الجدول يملأ العرض المتاح بالكامل (1150 بكسل)
- **تحسين الموقع:** رفع الجدول إلى موقع 220 بكسل مع ارتفاع 390 بكسل
