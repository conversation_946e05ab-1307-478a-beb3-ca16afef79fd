# منظم أعمال إدارة المدرسة

## وصف البرنامج
برنامج منظم أعمال إدارة المدرسة هو تطبيق Windows Forms مكتوب بلغة C# لإدارة بيانات المعلمين في المدرسة.

## المميزات

### النافذة الرئيسية
- **عنوان محسن:** "منظم أعمال إدارة المدرسة" مع تصميم عصري
- **ترتيب الأزرار من الأعلى للأسفل:**
  - **زر إعدادات البرنامج:** لفتح نافذة الإعدادات (رمادي Bootstrap)
  - **زر التوزيع الآلي:** لوظيفة التوزيع الآلي (أخضر Bootstrap)
  - **زر الخروج:** لإغلاق التطبيق (أحمر Bootstrap)
- **تصميم حديث:** ألوان Bootstrap مع تأثيرات تفاعلية وخلفية محسنة

### نافذة إعدادات البرنامج
- **نافذة محسنة:** حجم 500x400 بكسل مع تصميم حديث وتخطيط أفقي
- **تخطيط الأزرار في صفوف أفقية:**
  - **الصف الأول:** بيانات المدرسة (أزرق) + إضافة تخصص (أخضر)
  - **الصف الثاني:** إضافة مرحلة (أصفر) + إضافة عمل (رمادي)
  - **الصف الثالث:** إضافة مسؤول متابعة (سماوي) - في المنتصف
  - **الصف الرابع:** الخروج من النافذة (أحمر) - في المنتصف
- **مواصفات الأزرار:**
  - **الحجم:** 200x50 بكسل لكل زر
  - **الخط:** Arial حجم 12 Bold
  - **الألوان:** Bootstrap مختلفة لكل زر
  - **المسافات:** 30 بكسل من الحواف، 20 بكسل بين الأزرار
- **الوظائف:**
  - **الأزرار الخمسة الأولى:** تعرض رسائل "قيد التطوير"
  - **زر الخروج:** يغلق نافذة الإعدادات ويعود للنافذة الرئيسية



## كيفية تشغيل البرنامج

### المتطلبات
- .NET 6.0 أو أحدث
- Windows OS

### التشغيل
```bash
# بناء المشروع
dotnet build SchoolManagement.csproj

# تشغيل البرنامج
dotnet run --project SchoolManagement.csproj
```

## بنية المشروع
- `Program.cs` - نقطة دخول التطبيق
- `MainForm.cs` - النافذة الرئيسية
- `SettingsForm.cs` - نافذة إعدادات البرنامج
- `SchoolDataForm.cs` - نافذة بيانات المدرسة
- `AutoDistributionForm.cs` - نافذة التوزيع الآلي للمهام
- `ReportForm.cs` - نافذة عرض التقارير
- `SchoolManagement.csproj` - ملف المشروع



## الواجهة
- واجهة باللغة العربية مع دعم الكتابة من اليمين إلى اليسار
- **تصميم حديث يشبه CSS:** تنسيق احترافي مستوحى من تقنيات الويب الحديثة
- **مجموعات منظمة:** حقول الإدخال والأزرار في مجموعات منفصلة مع خلفيات مميزة
- حقول الإدخال في صف أفقي واحد مرتبة من اليمين إلى اليسار: (الاسم، رقم السجل، التخصص، المرحلة، عدد الحصص، رقم الجوال)
- **الأزرار متوسطة تماماً** في مجموعة العمليات مع تباعد متساوي
- **ألوان Bootstrap:** استخدام نظام ألوان Bootstrap للأزرار (أخضر للحفظ، أزرق للتعديل، أحمر للحذف، إلخ)
- **تأثيرات تفاعلية:** تغيير لون الأزرار عند التمرير عليها (hover effects)
- جدول محسن مع تنسيق CSS-like وألوان متدرجة
- عرض شاشة مُحسن ومضغوط (1000 بكسل) لسهولة الاستخدام
- خط موحد Arial بحجم 12 Bold في جميع أجزاء النافذة لوضوح أفضل
- **نظام ألوان متسق:** استخدام لوحة ألوان موحدة عبر التطبيق

## التصميم المحسن (CSS-like)
- **لوحة الألوان:**
  - خلفية التطبيق: `#F8F9FA` (رمادي فاتح)
  - نص رئيسي: `#495057` (رمادي داكن)
  - حدود: `#DEE2E6` (رمادي فاتح للحدود)

- **ألوان الأزرار (Bootstrap Colors):**
  - حفظ: `#28A745` (أخضر النجاح)
  - تعديل: `#007BFF` (أزرق أساسي)
  - حذف: `#DC3545` (أحمر الخطر)
  - مسح الحقول: `#FFC107` (أصفر التحذير)
  - تقرير: `#6C757D` (رمادي ثانوي)

- **تأثيرات تفاعلية:**
  - تغيير لون الأزرار عند التمرير
  - مؤشر اليد عند التمرير على الأزرار
  - حدود مسطحة للعناصر

- **تخطيط متجاوب:**
  - توسيط الأزرار تلقائياً في المجموعة
  - حسابات CSS-like للمسافات والتوسيط
  - تنسيق الجدول مع ألوان متدرجة

## تحسينات التخطيط الجديدة
- **عناوين الحقول المحسنة:**
  - العناوين فوق الحقول مباشرة بدلاً من جانبها
  - توسيط العناوين أفقياً فوق كل حقل
  - إزالة النقطتين من العناوين لمظهر أنظف
  - تباعد مثالي بين العناوين والحقول

- **توسيط الأزرار المثالي:**
  - حسابات رياضية دقيقة للتوسيط الأفقي والعمودي
  - المسافة من الأزرار إلى حدود المجموعة متساوية من جميع الجهات
  - توزيع متساوي للأزرار داخل المجموعة
  - مسافات محسوبة بدقة لمظهر احترافي

## الميزات الجديدة
### زر التوزيع الآلي
- **الموقع:** في الشاشة الرئيسية بين زر إدارة المعلمين وزر الخروج
- **التصميم:** أخضر Bootstrap مع تأثيرات تفاعلية
- **الوظيفة الحالية:** رسالة إعلامية تشير إلى أن الوظيفة ستكون متاحة قريباً
- **الهدف المستقبلي:** توزيع المعلمين على الفصول والمواد تلقائياً
- **التكامل:** مصمم ليتكامل مع نظام إدارة المعلمين الحالي

### نافذة التوزيع الآلي للمهام
- **مجموعة العمليات المحسنة (4 أزرار):**
  - **التوزيع الآلي:** توزيع المهام تلقائياً على المعلمين (أخضر Bootstrap)
  - **تفريغ جميع الحقول:** مسح جميع التكليفات الحالية (أصفر Bootstrap)
  - **إعادة التوزيع الآلي:** إعادة توزيع المهام مع استبدال التوزيع الحالي (أزرق Bootstrap)
  - **تقرير:** إنشاء تقرير بتصميم CSS حديث لبيانات الجدول المعروضة (رمادي Bootstrap)
  - الأزرار متوسطة في صف أفقي واحد مع ألوان Bootstrap

- **قسم البحث والتصفية الشامل:**
  - **تخطيط صف واحد:** جميع عناصر البحث في صف أفقي واحد متناسق
  - **الترتيب المحسن من اليمين:** البحث بالاسم → مسؤول المتابعة → تاريخ معين → من تاريخ → إلى تاريخ → مستوى التنفيذ → زر بحث → زر مسح البحث
  - **البحث بمستوى التنفيذ:** خيارات "الكل"، "نعم"، "لا"، "غير محدد"
  - **تصميم مضغوط:** ارتفاع مجموعة البحث 80 بكسل مع مسافة 120 بكسل بين العناصر
  - **خطوط مصغرة:** حجم خط 9 للعناوين والحقول لتوفير المساحة

- **جدول التوزيع المحسن والقابل للتعديل:**
  - **أحجام أعمدة محسنة لملء الجدول:**
    - اسم المعلم: 200 بكسل (موسع لعرض أفضل للأسماء) - غير قابل للتعديل
    - تحضير1/2، واجبات1/2، زيارة1/2: 110 بكسل لكل عمود (قابل للتعديل)
    - مسؤول المتابعة: 190 بكسل (موسع لملء المساحة المتبقية) - قائمة منسدلة
    - مستوى التنفيذ: 100 بكسل - خيارات "نعم" أو "لا"
  - **تخطيط محسن:** ارتفاع الجدول 390 بكسل مع موقع محسن
  - **توزيع متوازن:** أحجام الأعمدة موزعة بشكل مثالي لاستغلال المساحة

- **ميزات التعديل المباشر:**
  - **تعديل بالنقر:** النقر على أي حقل قابل للتعديل لبدء التعديل
  - **حفظ تلقائي:** الحفظ التلقائي عند مغادرة الحقل
  - **مؤشر بصري:** تغيير لون الحقل إلى أخضر فاتح عند الحفظ
  - **التحقق من صحة البيانات:** التحقق من صحة التواريخ المدخلة
  - **قوائم منسدلة:** للمسؤولين ومستوى التنفيذ
  - **اختيار واحد فقط:** في مستوى التنفيذ (نعم أو لا)

- **الميزات العامة:**
  - تصميم CSS حديث مع ألوان Bootstrap
  - بيانات تجريبية للاختبار
  - تصفية ديناميكية للبيانات
  - عداد السجلات المعروضة
  - رسائل تأكيد للعمليات الحساسة

## ميزة التقرير المحسن بتصميم CSS
### تصميم احترافي يشبه تقارير CSS الحديثة
- **رأس التقرير الأنيق:**
  - إطار مزخرف بأحرف Unicode (╔═══╗)
  - عنوان مركزي واضح
  - تاريخ ووقت الإنشاء

- **جدول بيانات بتصميم CSS:**
  - حدود احترافية باستخدام أحرف Unicode
  - رأس جدول واضح مع فواصل
  - صفوف منظمة مع حدود بين كل صف
  - عرض "-" للحقول الفارغة لوضوح أفضل

- **تذييل التقرير:**
  - إطار ملخص أنيق
  - عدد السجلات المعروضة
  - تاريخ ووقت الإنشاء التفصيلي

- **ميزات التقرير:**
  - يعرض البيانات المفلترة فقط (حسب البحث الحالي)
  - تنسيق احترافي قابل للطباعة
  - حفظ كملف نصي مع تنسيق محفوظ
  - سهولة القراءة والفهم

## تحسينات التخطيط الجديدة في نافذة التوزيع الآلي
### إعادة تنظيم قسم البحث
- **صف واحد متناسق:** جميع عناصر البحث في صف أفقي واحد بدلاً من صفين
- **ترتيب منطقي من اليمين:**
  1. البحث بالاسم (120 بكسل)
  2. مسؤول المتابعة (120 بكسل)
  3. تاريخ معين (120 بكسل)
  4. من تاريخ (120 بكسل)
  5. إلى تاريخ (120 بكسل)
  6. زر بحث (60 بكسل)
  7. زر مسح البحث (65 بكسل)
- **مساحة مضغوطة:** تقليل ارتفاع مجموعة البحث من 120 إلى 80 بكسل
- **خطوط مصغرة:** استخدام خط بحجم 9 لتوفير المساحة

### تحسين أحجام أعمدة الجدول لملء المساحة
- **استغلال كامل للعرض:** إعادة توزيع أحجام الأعمدة لملء عرض الجدول بالكامل
- **توسيع الأعمدة الرئيسية:**
  - اسم المعلم: من 160 إلى 200 بكسل (لعرض أفضل للأسماء الطويلة)
  - مسؤول المتابعة: من 130 إلى 190 بكسل (لملء المساحة المتبقية)
  - مستوى التنفيذ: يبقى 100 بكسل (مناسب للخيارات المحدودة)
- **توزيع مثالي:** الجدول يملأ العرض المتاح بالكامل (1150 بكسل)
- **تحسين الموقع:** رفع الجدول إلى موقع 220 بكسل مع ارتفاع 390 بكسل
