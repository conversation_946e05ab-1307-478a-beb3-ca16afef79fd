using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class AddSupervisorForm : Form
    {
        // مسار ملف حفظ مسؤولي المتابعة
        private readonly string supervisorsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Supervisors.txt");

        // Controls
        private TextBox supervisorTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;
        private DataGridView supervisorsDataGridView;
        private List<string> supervisors;

        public AddSupervisorForm()
        {
            supervisors = new List<string>();
            InitializeComponent();
            LoadSupervisors();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إضافة مسؤول متابعة";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إضافة مسؤول متابعة";
            titleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(500, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Left side - Input section
            CreateInputSection();

            // Right side - DataGridView
            CreateDataGridView();

            this.ResumeLayout(false);
        }

        private void CreateInputSection()
        {
            // Supervisor Label
            Label supervisorLabel = new Label();
            supervisorLabel.Text = "مسؤول المتابعة";
            supervisorLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorLabel.Size = new Size(120, 25);
            supervisorLabel.Location = new Point(30, 70);
            this.Controls.Add(supervisorLabel);

            // Supervisor TextBox
            supervisorTextBox = new TextBox();
            supervisorTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorTextBox.Size = new Size(200, 30);
            supervisorTextBox.Location = new Point(30, 100);
            this.Controls.Add(supervisorTextBox);

            // Buttons - vertical layout
            CreateButtons();
        }

        private void CreateButtons()
        {
            // Save Button
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(200, 35);
            saveButton.Location = new Point(30, 130);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // Edit Button
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(200, 35);
            editButton.Location = new Point(30, 175);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Delete Button
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(200, 35);
            deleteButton.Location = new Point(30, 220);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // Close Button
            closeButton = new Button();
            closeButton.Text = "خروج من النافذة";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(200, 35);
            closeButton.Location = new Point(30, 265);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);
        }

        private void CreateDataGridView()
        {
            // DataGridView for supervisors
            supervisorsDataGridView = new DataGridView();
            supervisorsDataGridView.Location = new Point(260, 70);
            supervisorsDataGridView.Size = new Size(200, 230);
            supervisorsDataGridView.BackgroundColor = Color.White;
            supervisorsDataGridView.BorderStyle = BorderStyle.FixedSingle;
            supervisorsDataGridView.AllowUserToAddRows = false;
            supervisorsDataGridView.AllowUserToDeleteRows = false;
            supervisorsDataGridView.ReadOnly = true;
            supervisorsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            supervisorsDataGridView.MultiSelect = false;
            supervisorsDataGridView.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorsDataGridView.RowHeadersVisible = false;
            supervisorsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            
            // زيادة ارتفاع عنوان الجدول
            supervisorsDataGridView.ColumnHeadersHeight = 40;
            supervisorsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

            // Add column
            DataGridViewTextBoxColumn supervisorColumn = new DataGridViewTextBoxColumn();
            supervisorColumn.Name = "Supervisor";
            supervisorColumn.HeaderText = "مسؤول المتابعة";
            supervisorColumn.DataPropertyName = "Supervisor";
            supervisorColumn.DefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorsDataGridView.Columns.Add(supervisorColumn);

            // Set header font
            supervisorsDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);

            // Event handlers
            supervisorsDataGridView.SelectionChanged += SupervisorsDataGridView_SelectionChanged;

            this.Controls.Add(supervisorsDataGridView);
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                string newSupervisor = supervisorTextBox.Text.Trim();
                
                if (supervisors.Contains(newSupervisor))
                {
                    MessageBox.Show("هذا المسؤول موجود مسبقاً!", "مسؤول مكرر", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                supervisors.Add(newSupervisor);
                SaveSupervisors();
                RefreshDataGridView();
                supervisorTextBox.Clear();
                
                MessageBox.Show("تم حفظ مسؤول المتابعة بنجاح!", "تأكيد الحفظ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (supervisorsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مسؤول للتعديل", "لم يتم اختيار مسؤول", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            int selectedIndex = supervisorsDataGridView.SelectedRows[0].Index;
            string newSupervisor = supervisorTextBox.Text.Trim();
            
            if (supervisors.Contains(newSupervisor) && supervisors[selectedIndex] != newSupervisor)
            {
                MessageBox.Show("هذا المسؤول موجود مسبقاً!", "مسؤول مكرر", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            supervisors[selectedIndex] = newSupervisor;
            SaveSupervisors();
            RefreshDataGridView();
            supervisorTextBox.Clear();
            
            MessageBox.Show("تم تعديل مسؤول المتابعة بنجاح!", "تأكيد التعديل", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (supervisorsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مسؤول للحذف", "لم يتم اختيار مسؤول", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = supervisorsDataGridView.SelectedRows[0].Index;
            string selectedSupervisor = supervisors[selectedIndex];

            var result = MessageBox.Show($"هل أنت متأكد من حذف مسؤول المتابعة '{selectedSupervisor}'؟", 
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            
            if (result == DialogResult.Yes)
            {
                supervisors.RemoveAt(selectedIndex);
                SaveSupervisors();
                RefreshDataGridView();
                supervisorTextBox.Clear();
                
                MessageBox.Show("تم حذف مسؤول المتابعة بنجاح!", "تأكيد الحذف", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void SupervisorsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (supervisorsDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = supervisorsDataGridView.SelectedRows[0].Index;
                if (selectedIndex < supervisors.Count)
                {
                    supervisorTextBox.Text = supervisors[selectedIndex];
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(supervisorTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم مسؤول المتابعة", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                supervisorTextBox.Focus();
                return false;
            }
            return true;
        }

        private void SaveSupervisors()
        {
            try
            {
                string directory = Path.GetDirectoryName(supervisorsFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllLines(supervisorsFilePath, supervisors);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ مسؤولي المتابعة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSupervisors()
        {
            try
            {
                if (File.Exists(supervisorsFilePath))
                {
                    supervisors = File.ReadAllLines(supervisorsFilePath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                }
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل مسؤولي المتابعة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            supervisorsDataGridView.DataSource = null;
            supervisorsDataGridView.DataSource = supervisors.Select(s => new { Supervisor = s }).ToList();
        }
    }
}
