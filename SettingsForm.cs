using System;
using System.Drawing;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class SettingsForm : Form
    {
        public SettingsForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "الإعدادات";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إعدادات النظام";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87);
            titleLabel.Size = new Size(400, 40);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // School Data Button
            Button schoolDataButton = new Button();
            schoolDataButton.Text = "بيانات المدرسة";
            schoolDataButton.Font = new Font("Arial", 12, FontStyle.Bold);
            schoolDataButton.Size = new Size(180, 40);
            schoolDataButton.Location = new Point(50, 80);
            schoolDataButton.BackColor = Color.FromArgb(0, 123, 255);
            schoolDataButton.ForeColor = Color.White;
            schoolDataButton.FlatStyle = FlatStyle.Flat;
            schoolDataButton.FlatAppearance.BorderSize = 0;
            schoolDataButton.Click += SchoolDataButton_Click;
            this.Controls.Add(schoolDataButton);

            // Add Specialization Button
            Button addSpecializationButton = new Button();
            addSpecializationButton.Text = "إضافة تخصص";
            addSpecializationButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addSpecializationButton.Size = new Size(180, 40);
            addSpecializationButton.Location = new Point(270, 80);
            addSpecializationButton.BackColor = Color.FromArgb(40, 167, 69);
            addSpecializationButton.ForeColor = Color.White;
            addSpecializationButton.FlatStyle = FlatStyle.Flat;
            addSpecializationButton.FlatAppearance.BorderSize = 0;
            addSpecializationButton.Click += AddSpecializationButton_Click;
            this.Controls.Add(addSpecializationButton);

            // Add Stage Button
            Button addStageButton = new Button();
            addStageButton.Text = "إضافة مرحلة";
            addStageButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addStageButton.Size = new Size(180, 40);
            addStageButton.Location = new Point(50, 140);
            addStageButton.BackColor = Color.FromArgb(255, 193, 7);
            addStageButton.ForeColor = Color.Black;
            addStageButton.FlatStyle = FlatStyle.Flat;
            addStageButton.FlatAppearance.BorderSize = 0;
            addStageButton.Click += AddStageButton_Click;
            this.Controls.Add(addStageButton);

            // Add Work Button
            Button addWorkButton = new Button();
            addWorkButton.Text = "إضافة عمل";
            addWorkButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addWorkButton.Size = new Size(180, 40);
            addWorkButton.Location = new Point(270, 140);
            addWorkButton.BackColor = Color.FromArgb(220, 53, 69);
            addWorkButton.ForeColor = Color.White;
            addWorkButton.FlatStyle = FlatStyle.Flat;
            addWorkButton.FlatAppearance.BorderSize = 0;
            addWorkButton.Click += AddWorkButton_Click;
            this.Controls.Add(addWorkButton);

            // Add Supervisor Button
            Button addSupervisorButton = new Button();
            addSupervisorButton.Text = "إضافة مشرف";
            addSupervisorButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addSupervisorButton.Size = new Size(180, 40);
            addSupervisorButton.Location = new Point(50, 200);
            addSupervisorButton.BackColor = Color.FromArgb(108, 117, 125);
            addSupervisorButton.ForeColor = Color.White;
            addSupervisorButton.FlatStyle = FlatStyle.Flat;
            addSupervisorButton.FlatAppearance.BorderSize = 0;
            addSupervisorButton.Click += AddSupervisorButton_Click;
            this.Controls.Add(addSupervisorButton);

            // Exit Button
            Button exitButton = new Button();
            exitButton.Text = "إغلاق النافذة";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(180, 40);
            exitButton.Location = new Point(270, 200);
            exitButton.BackColor = Color.FromArgb(108, 117, 125);
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            this.ResumeLayout(false);
        }

        private void SchoolDataButton_Click(object sender, EventArgs e)
        {
            SchoolDataForm schoolDataForm = new SchoolDataForm();
            schoolDataForm.ShowDialog();
        }

        private void AddSpecializationButton_Click(object sender, EventArgs e)
        {
            SpecializationForm specializationForm = new SpecializationForm();
            specializationForm.ShowDialog();
        }

        private void AddStageButton_Click(object sender, EventArgs e)
        {
            StageForm stageForm = new StageForm();
            stageForm.ShowDialog();
        }

        private void AddWorkButton_Click(object sender, EventArgs e)
        {
            WorkForm workForm = new WorkForm();
            workForm.ShowDialog();
        }

        private void AddSupervisorButton_Click(object sender, EventArgs e)
        {
            SupervisorForm supervisorForm = new SupervisorForm();
            supervisorForm.ShowDialog();
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
