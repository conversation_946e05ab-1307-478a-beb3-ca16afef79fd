using System;
using System.Drawing;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class SettingsForm : Form
    {
        public SettingsForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إعدادات البرنامج";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إعدادات البرنامج";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(400, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // الصف الأول: إضافة تخصص (يمين) + بيانات المدرسة (يسار)
            // Add Subject Button (على اليمين)
            Button addSubjectButton = new Button();
            addSubjectButton.Text = "إضافة تخصص";
            addSubjectButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addSubjectButton.Size = new Size(200, 50);
            addSubjectButton.Location = new Point(270, 80);
            addSubjectButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            addSubjectButton.ForeColor = Color.White;
            addSubjectButton.FlatStyle = FlatStyle.Flat;
            addSubjectButton.FlatAppearance.BorderSize = 0;
            addSubjectButton.Cursor = Cursors.Hand;
            addSubjectButton.Click += AddSubjectButton_Click;
            this.Controls.Add(addSubjectButton);

            // School Data Button (على اليسار)
            Button schoolDataButton = new Button();
            schoolDataButton.Text = "بيانات المدرسة";
            schoolDataButton.Font = new Font("Arial", 12, FontStyle.Bold);
            schoolDataButton.Size = new Size(200, 50);
            schoolDataButton.Location = new Point(50, 80);
            schoolDataButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            schoolDataButton.ForeColor = Color.White;
            schoolDataButton.FlatStyle = FlatStyle.Flat;
            schoolDataButton.FlatAppearance.BorderSize = 0;
            schoolDataButton.Cursor = Cursors.Hand;
            schoolDataButton.Click += SchoolDataButton_Click;
            this.Controls.Add(schoolDataButton);

            // الصف الثاني: إضافة مرحلة (يمين) + إضافة عمل (يسار)
            // Add Stage Button (على اليمين)
            Button addStageButton = new Button();
            addStageButton.Text = "إضافة مرحلة";
            addStageButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addStageButton.Size = new Size(200, 50);
            addStageButton.Location = new Point(270, 150);
            addStageButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning yellow
            addStageButton.ForeColor = Color.Black;
            addStageButton.FlatStyle = FlatStyle.Flat;
            addStageButton.FlatAppearance.BorderSize = 0;
            addStageButton.Cursor = Cursors.Hand;
            addStageButton.Click += AddStageButton_Click;
            this.Controls.Add(addStageButton);

            // Add Work Button (على اليسار)
            Button addWorkButton = new Button();
            addWorkButton.Text = "إضافة عمل";
            addWorkButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addWorkButton.Size = new Size(200, 50);
            addWorkButton.Location = new Point(50, 150);
            addWorkButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            addWorkButton.ForeColor = Color.White;
            addWorkButton.FlatStyle = FlatStyle.Flat;
            addWorkButton.FlatAppearance.BorderSize = 0;
            addWorkButton.Cursor = Cursors.Hand;
            addWorkButton.Click += AddWorkButton_Click;
            this.Controls.Add(addWorkButton);

            // الصف الثالث: إضافة مسؤول متابعة (يسار) + الخروج من النافذة (يمين)
            // Add Supervisor Button (على اليسار)
            Button addSupervisorButton = new Button();
            addSupervisorButton.Text = "إضافة مسؤول متابعة";
            addSupervisorButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addSupervisorButton.Size = new Size(200, 50);
            addSupervisorButton.Location = new Point(50, 220);
            addSupervisorButton.BackColor = Color.FromArgb(23, 162, 184); // Bootstrap info cyan
            addSupervisorButton.ForeColor = Color.White;
            addSupervisorButton.FlatStyle = FlatStyle.Flat;
            addSupervisorButton.FlatAppearance.BorderSize = 0;
            addSupervisorButton.Cursor = Cursors.Hand;
            addSupervisorButton.Click += AddSupervisorButton_Click;
            this.Controls.Add(addSupervisorButton);

            // Exit Button (على اليمين)
            Button exitButton = new Button();
            exitButton.Text = "الخروج من النافذة";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(200, 50);
            exitButton.Location = new Point(270, 220);
            exitButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Cursor = Cursors.Hand;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            this.ResumeLayout(false);
        }

        private void SchoolDataButton_Click(object sender, EventArgs e)
        {
            SchoolDataForm schoolDataForm = new SchoolDataForm();
            schoolDataForm.ShowDialog();
        }

        private void AddSubjectButton_Click(object sender, EventArgs e)
        {
            AddSubjectForm addSubjectForm = new AddSubjectForm();
            addSubjectForm.ShowDialog();
        }

        private void AddStageButton_Click(object sender, EventArgs e)
        {
            AddStageForm addStageForm = new AddStageForm();
            addStageForm.ShowDialog();
        }

        private void AddWorkButton_Click(object sender, EventArgs e)
        {
            AddWorkForm addWorkForm = new AddWorkForm();
            addWorkForm.ShowDialog();
        }

        private void AddSupervisorButton_Click(object sender, EventArgs e)
        {
            AddSupervisorForm addSupervisorForm = new AddSupervisorForm();
            addSupervisorForm.ShowDialog();
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
