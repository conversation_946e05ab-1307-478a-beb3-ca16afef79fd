using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class AddStageForm : Form
    {
        // مسار ملف حفظ المراحل
        private readonly string stagesFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Stages.txt");

        // Controls
        private TextBox stageTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;
        private DataGridView stagesDataGridView;
        private List<string> stages;

        public AddStageForm()
        {
            stages = new List<string>();
            InitializeComponent();
            LoadStages();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إضافة مرحلة";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إضافة مرحلة";
            titleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(500, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Left side - Input section
            CreateInputSection();

            // Right side - DataGridView
            CreateDataGridView();

            this.ResumeLayout(false);
        }

        private void CreateInputSection()
        {
            // Stage Label
            Label stageLabel = new Label();
            stageLabel.Text = "المرحلة";
            stageLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            stageLabel.Size = new Size(100, 25);
            stageLabel.Location = new Point(30, 70);
            this.Controls.Add(stageLabel);

            // Stage TextBox
            stageTextBox = new TextBox();
            stageTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            stageTextBox.Size = new Size(200, 30);
            stageTextBox.Location = new Point(30, 100);
            this.Controls.Add(stageTextBox);

            // Buttons - vertical layout
            CreateButtons();
        }

        private void CreateButtons()
        {
            // Save Button
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(200, 35);
            saveButton.Location = new Point(30, 130);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // Edit Button
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(200, 35);
            editButton.Location = new Point(30, 175);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Delete Button
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(200, 35);
            deleteButton.Location = new Point(30, 220);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // Close Button
            closeButton = new Button();
            closeButton.Text = "خروج من النافذة";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(200, 35);
            closeButton.Location = new Point(30, 265);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);
        }

        private void CreateDataGridView()
        {
            // DataGridView for stages
            stagesDataGridView = new DataGridView();
            stagesDataGridView.Location = new Point(260, 70);
            stagesDataGridView.Size = new Size(200, 230);
            stagesDataGridView.BackgroundColor = Color.White;
            stagesDataGridView.BorderStyle = BorderStyle.FixedSingle;
            stagesDataGridView.AllowUserToAddRows = false;
            stagesDataGridView.AllowUserToDeleteRows = false;
            stagesDataGridView.ReadOnly = true;
            stagesDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            stagesDataGridView.MultiSelect = false;
            stagesDataGridView.Font = new Font("Arial", 12, FontStyle.Bold);
            stagesDataGridView.RowHeadersVisible = false;
            stagesDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            
            // زيادة ارتفاع عنوان الجدول
            stagesDataGridView.ColumnHeadersHeight = 40;
            stagesDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

            // Add column
            DataGridViewTextBoxColumn stageColumn = new DataGridViewTextBoxColumn();
            stageColumn.Name = "Stage";
            stageColumn.HeaderText = "المرحلة";
            stageColumn.DataPropertyName = "Stage";
            stageColumn.DefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            stagesDataGridView.Columns.Add(stageColumn);

            // Set header font
            stagesDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);

            // Event handlers
            stagesDataGridView.SelectionChanged += StagesDataGridView_SelectionChanged;

            this.Controls.Add(stagesDataGridView);
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                string newStage = stageTextBox.Text.Trim();
                
                if (stages.Contains(newStage))
                {
                    MessageBox.Show("هذه المرحلة موجودة مسبقاً!", "مرحلة مكررة", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                stages.Add(newStage);
                SaveStages();
                RefreshDataGridView();
                stageTextBox.Clear();
                
                MessageBox.Show("تم حفظ المرحلة بنجاح!", "تأكيد الحفظ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (stagesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مرحلة للتعديل", "لم يتم اختيار مرحلة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            int selectedIndex = stagesDataGridView.SelectedRows[0].Index;
            string newStage = stageTextBox.Text.Trim();
            
            if (stages.Contains(newStage) && stages[selectedIndex] != newStage)
            {
                MessageBox.Show("هذه المرحلة موجودة مسبقاً!", "مرحلة مكررة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            stages[selectedIndex] = newStage;
            SaveStages();
            RefreshDataGridView();
            stageTextBox.Clear();
            
            MessageBox.Show("تم تعديل المرحلة بنجاح!", "تأكيد التعديل", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (stagesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مرحلة للحذف", "لم يتم اختيار مرحلة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = stagesDataGridView.SelectedRows[0].Index;
            string selectedStage = stages[selectedIndex];

            var result = MessageBox.Show($"هل أنت متأكد من حذف المرحلة '{selectedStage}'؟", 
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            
            if (result == DialogResult.Yes)
            {
                stages.RemoveAt(selectedIndex);
                SaveStages();
                RefreshDataGridView();
                stageTextBox.Clear();
                
                MessageBox.Show("تم حذف المرحلة بنجاح!", "تأكيد الحذف", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void StagesDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (stagesDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = stagesDataGridView.SelectedRows[0].Index;
                if (selectedIndex < stages.Count)
                {
                    stageTextBox.Text = stages[selectedIndex];
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(stageTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المرحلة", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                stageTextBox.Focus();
                return false;
            }
            return true;
        }

        private void SaveStages()
        {
            try
            {
                string directory = Path.GetDirectoryName(stagesFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllLines(stagesFilePath, stages);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ المراحل: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadStages()
        {
            try
            {
                if (File.Exists(stagesFilePath))
                {
                    stages = File.ReadAllLines(stagesFilePath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                }
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل المراحل: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            stagesDataGridView.DataSource = null;
            stagesDataGridView.DataSource = stages.Select(s => new { Stage = s }).ToList();
        }
    }
}
