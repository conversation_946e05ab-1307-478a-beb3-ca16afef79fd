using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class ViewEmployeeDataForm : Form
    {
        // مسار ملف حفظ بيانات الموظفين
        private readonly string employeesFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Employees.txt");

        // Controls
        private GroupBox actionsGroupBox;
        private TextBox searchTextBox;
        private Button clearSearchButton;
        private Button addButton;
        private Button editButton;
        private Button reportButton;
        private Button closeButton;
        private DataGridView employeesDataGridView;
        private List<Employee> employees;
        private List<Employee> filteredEmployees;

        // Static instance for singleton pattern
        public static ViewEmployeeDataForm? Instance { get; private set; }

        public ViewEmployeeDataForm()
        {
            employees = new List<Employee>();
            filteredEmployees = new List<Employee>();
            Instance = this;
            InitializeComponent();
            LoadEmployees();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            Instance = null;
            base.OnFormClosed(e);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "عرض بيانات الموظفين";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "عرض بيانات الموظفين";
            titleLabel.Font = new Font("Arial", 14, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(1100, 35);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Create actions group box
            CreateActionsGroupBox();

            // Create search controls
            CreateSearchControls();

            // Create DataGridView
            CreateDataGridView();

            this.ResumeLayout(false);
        }



        private void CreateActionsGroupBox()
        {
            actionsGroupBox = new GroupBox();
            actionsGroupBox.Text = "العمليات";
            actionsGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            actionsGroupBox.Size = new Size(1100, 80);
            actionsGroupBox.Location = new Point(50, 70);
            actionsGroupBox.ForeColor = Color.FromArgb(52, 58, 64);

            // Add Button (أقصى اليمين)
            addButton = new Button();
            addButton.Text = "إضافة";
            addButton.Font = new Font("Arial", 12, FontStyle.Bold);
            addButton.Size = new Size(120, 40);
            addButton.Location = new Point(950, 25);
            addButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            addButton.ForeColor = Color.White;
            addButton.FlatStyle = FlatStyle.Flat;
            addButton.FlatAppearance.BorderSize = 0;
            addButton.Cursor = Cursors.Hand;
            addButton.Click += AddButton_Click;
            actionsGroupBox.Controls.Add(addButton);

            // Edit Button
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(120, 40);
            editButton.Location = new Point(810, 25);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            actionsGroupBox.Controls.Add(editButton);

            // Report Button
            reportButton = new Button();
            reportButton.Text = "تقرير";
            reportButton.Font = new Font("Arial", 12, FontStyle.Bold);
            reportButton.Size = new Size(120, 40);
            reportButton.Location = new Point(670, 25);
            reportButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning yellow
            reportButton.ForeColor = Color.Black;
            reportButton.FlatStyle = FlatStyle.Flat;
            reportButton.FlatAppearance.BorderSize = 0;
            reportButton.Cursor = Cursors.Hand;
            reportButton.Click += ReportButton_Click;
            actionsGroupBox.Controls.Add(reportButton);

            // Close Button (أقصى اليسار)
            closeButton = new Button();
            closeButton.Text = "إغلاق";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(120, 40);
            closeButton.Location = new Point(530, 25);
            closeButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            actionsGroupBox.Controls.Add(closeButton);

            this.Controls.Add(actionsGroupBox);
        }

        private void CreateSearchControls()
        {
            // Search Label
            Label searchLabel = new Label();
            searchLabel.Text = "البحث بالاسم:";
            searchLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            searchLabel.Size = new Size(100, 25);
            searchLabel.Location = new Point(50, 165);
            searchLabel.ForeColor = Color.FromArgb(52, 58, 64);
            this.Controls.Add(searchLabel);

            // Search TextBox
            searchTextBox = new TextBox();
            searchTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchTextBox.Size = new Size(300, 30);
            searchTextBox.Location = new Point(160, 162);
            searchTextBox.TextChanged += SearchTextBox_TextChanged;
            this.Controls.Add(searchTextBox);

            // Clear Search Button
            clearSearchButton = new Button();
            clearSearchButton.Text = "مسح البحث";
            clearSearchButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearSearchButton.Size = new Size(120, 35);
            clearSearchButton.Location = new Point(480, 160);
            clearSearchButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            clearSearchButton.ForeColor = Color.White;
            clearSearchButton.FlatStyle = FlatStyle.Flat;
            clearSearchButton.FlatAppearance.BorderSize = 0;
            clearSearchButton.Cursor = Cursors.Hand;
            clearSearchButton.Click += ClearSearchButton_Click;
            this.Controls.Add(clearSearchButton);
        }

        private void CreateDataGridView()
        {
            // DataGridView for employees
            employeesDataGridView = new DataGridView();
            employeesDataGridView.Location = new Point(50, 210);
            employeesDataGridView.Size = new Size(1100, 340);
            employeesDataGridView.BackgroundColor = Color.White;
            employeesDataGridView.BorderStyle = BorderStyle.FixedSingle;
            employeesDataGridView.AllowUserToAddRows = false;
            employeesDataGridView.AllowUserToDeleteRows = false;
            employeesDataGridView.ReadOnly = true;
            employeesDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            employeesDataGridView.MultiSelect = false;
            employeesDataGridView.Font = new Font("Arial", 11, FontStyle.Bold);
            employeesDataGridView.RowHeadersVisible = false;
            employeesDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            
            // زيادة ارتفاع عنوان الجدول
            employeesDataGridView.ColumnHeadersHeight = 40;
            employeesDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

            // Add columns
            AddDataGridViewColumns();

            // Set header font
            employeesDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            employeesDataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            employeesDataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;

            this.Controls.Add(employeesDataGridView);
        }

        private void AddDataGridViewColumns()
        {
            // Name column
            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "Name";
            nameColumn.HeaderText = "اسم الموظف";
            nameColumn.DataPropertyName = "Name";
            nameColumn.FillWeight = 20;
            employeesDataGridView.Columns.Add(nameColumn);

            // Civil ID column
            DataGridViewTextBoxColumn civilIdColumn = new DataGridViewTextBoxColumn();
            civilIdColumn.Name = "CivilId";
            civilIdColumn.HeaderText = "السجل المدني";
            civilIdColumn.DataPropertyName = "CivilId";
            civilIdColumn.FillWeight = 15;
            employeesDataGridView.Columns.Add(civilIdColumn);

            // Subject column
            DataGridViewTextBoxColumn subjectColumn = new DataGridViewTextBoxColumn();
            subjectColumn.Name = "Subject";
            subjectColumn.HeaderText = "التخصص";
            subjectColumn.DataPropertyName = "Subject";
            subjectColumn.FillWeight = 15;
            employeesDataGridView.Columns.Add(subjectColumn);

            // Work column
            DataGridViewTextBoxColumn workColumn = new DataGridViewTextBoxColumn();
            workColumn.Name = "Work";
            workColumn.HeaderText = "العمل الحالي";
            workColumn.DataPropertyName = "Work";
            workColumn.FillWeight = 15;
            employeesDataGridView.Columns.Add(workColumn);

            // Stage column
            DataGridViewTextBoxColumn stageColumn = new DataGridViewTextBoxColumn();
            stageColumn.Name = "Stage";
            stageColumn.HeaderText = "المرحلة";
            stageColumn.DataPropertyName = "Stage";
            stageColumn.FillWeight = 10;
            employeesDataGridView.Columns.Add(stageColumn);

            // Classes Count column
            DataGridViewTextBoxColumn classesColumn = new DataGridViewTextBoxColumn();
            classesColumn.Name = "ClassesCount";
            classesColumn.HeaderText = "عدد الحصص";
            classesColumn.DataPropertyName = "ClassesCount";
            classesColumn.FillWeight = 10;
            employeesDataGridView.Columns.Add(classesColumn);

            // Mobile column
            DataGridViewTextBoxColumn mobileColumn = new DataGridViewTextBoxColumn();
            mobileColumn.Name = "Mobile";
            mobileColumn.HeaderText = "رقم الجوال";
            mobileColumn.DataPropertyName = "Mobile";
            mobileColumn.FillWeight = 15;
            employeesDataGridView.Columns.Add(mobileColumn);

            // Supervisor column
            DataGridViewTextBoxColumn supervisorColumn = new DataGridViewTextBoxColumn();
            supervisorColumn.Name = "Supervisor";
            supervisorColumn.HeaderText = "مسؤول المتابعة";
            supervisorColumn.DataPropertyName = "Supervisor";
            supervisorColumn.FillWeight = 15;
            employeesDataGridView.Columns.Add(supervisorColumn);
        }



        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            FilterEmployees();
        }

        private void ClearSearchButton_Click(object sender, EventArgs e)
        {
            searchTextBox.Clear();
            FilterEmployees();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            EmployeeDataForm employeeDataForm = new EmployeeDataForm();
            employeeDataForm.ShowDialog();
            // Refresh data after adding
            LoadEmployees();
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (employeesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار موظف للتعديل", "لم يتم اختيار موظف",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = employeesDataGridView.SelectedRows[0].Index;
            if (selectedIndex < filteredEmployees.Count)
            {
                var selectedEmployee = filteredEmployees[selectedIndex];
                EmployeeDataForm employeeDataForm = new EmployeeDataForm();
                employeeDataForm.LoadEmployeeForEdit(selectedEmployee);
                employeeDataForm.ShowDialog();
                // Refresh data after editing
                LoadEmployees();
            }
        }

        private void ReportButton_Click(object sender, EventArgs e)
        {
            GenerateReport();
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void FilterEmployees()
        {
            string searchText = searchTextBox.Text.Trim().ToLower();

            if (string.IsNullOrEmpty(searchText))
            {
                filteredEmployees = new List<Employee>(employees);
            }
            else
            {
                filteredEmployees = employees.Where(emp =>
                    emp.Name.ToLower().Contains(searchText)).ToList();
            }

            RefreshDataGridView();
        }

        public void LoadEmployees()
        {
            try
            {
                employees.Clear();
                if (File.Exists(employeesFilePath))
                {
                    var lines = File.ReadAllLines(employeesFilePath).Where(s => !string.IsNullOrWhiteSpace(s));

                    foreach (var line in lines)
                    {
                        var parts = line.Split('|');
                        if (parts.Length == 8)
                        {
                            employees.Add(new Employee
                            {
                                Name = parts[0],
                                CivilId = parts[1],
                                Subject = parts[2],
                                Work = parts[3],
                                Stage = parts[4],
                                ClassesCount = parts[5],
                                Mobile = parts[6],
                                Supervisor = parts[7]
                            });
                        }
                    }
                }
                FilterEmployees();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            employeesDataGridView.DataSource = null;
            employeesDataGridView.DataSource = filteredEmployees;
        }

        private void GenerateReport()
        {
            if (filteredEmployees.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات لإنشاء التقرير", "تقرير فارغ",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string reportContent = GenerateEmployeeReport();
            ReportForm reportForm = new ReportForm(reportContent);
            reportForm.ShowDialog();
        }

        private string GenerateEmployeeReport()
        {
            var report = new System.Text.StringBuilder();

            // Header
            report.AppendLine("=".PadLeft(80, '='));
            report.AppendLine("تقرير بيانات الموظفين".PadLeft(50));
            report.AppendLine("=".PadLeft(80, '='));
            report.AppendLine();

            // Date and time
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}");
            report.AppendLine($"وقت التقرير: {DateTime.Now:HH:mm:ss}");
            report.AppendLine($"عدد الموظفين: {filteredEmployees.Count}");
            report.AppendLine();
            report.AppendLine("-".PadLeft(80, '-'));
            report.AppendLine();

            // Table header
            report.AppendLine(string.Format("{0,-20} {1,-12} {2,-15} {3,-15} {4,-10} {5,-8} {6,-12} {7,-15}",
                "الاسم", "السجل المدني", "التخصص", "العمل الحالي", "المرحلة", "الحصص", "الجوال", "مسؤول المتابعة"));
            report.AppendLine("-".PadLeft(120, '-'));

            // Employee data
            foreach (var employee in filteredEmployees)
            {
                report.AppendLine(string.Format("{0,-20} {1,-12} {2,-15} {3,-15} {4,-10} {5,-8} {6,-12} {7,-15}",
                    employee.Name.Length > 18 ? employee.Name.Substring(0, 18) + ".." : employee.Name,
                    employee.CivilId,
                    employee.Subject.Length > 13 ? employee.Subject.Substring(0, 13) + ".." : employee.Subject,
                    employee.Work.Length > 13 ? employee.Work.Substring(0, 13) + ".." : employee.Work,
                    employee.Stage.Length > 8 ? employee.Stage.Substring(0, 8) + ".." : employee.Stage,
                    employee.ClassesCount,
                    employee.Mobile,
                    employee.Supervisor.Length > 13 ? employee.Supervisor.Substring(0, 13) + ".." : employee.Supervisor));
            }

            report.AppendLine();
            report.AppendLine("-".PadLeft(80, '-'));
            report.AppendLine($"إجمالي عدد الموظفين: {filteredEmployees.Count}");
            report.AppendLine("=".PadLeft(80, '='));

            return report.ToString();
        }
    }
}
