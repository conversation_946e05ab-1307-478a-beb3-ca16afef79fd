using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class StageForm : Form
    {
        private TextBox stageTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button exitButton;
        private DataGridView stagesDataGridView;
        private List<string> stages;

        private readonly string stagesFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Stages.txt");

        public StageForm()
        {
            stages = new List<string>();
            InitializeComponent();
            LoadStages();
            RefreshDataGridView();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "إدارة المراحل";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إدارة المراحل";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87);
            titleLabel.Size = new Size(500, 40);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // Input Section
            Label inputLabel = new Label();
            inputLabel.Text = "اسم المرحلة:";
            inputLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            inputLabel.Size = new Size(100, 25);
            inputLabel.Location = new Point(50, 80);
            this.Controls.Add(inputLabel);

            stageTextBox = new TextBox();
            stageTextBox.Font = new Font("Arial", 12);
            stageTextBox.Size = new Size(200, 25);
            stageTextBox.Location = new Point(50, 110);
            this.Controls.Add(stageTextBox);

            // Buttons
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(80, 35);
            saveButton.Location = new Point(50, 150);
            saveButton.BackColor = Color.FromArgb(40, 167, 69);
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(80, 35);
            editButton.Location = new Point(50, 200);
            editButton.BackColor = Color.FromArgb(255, 193, 7);
            editButton.ForeColor = Color.Black;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(80, 35);
            deleteButton.Location = new Point(50, 250);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69);
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            exitButton = new Button();
            exitButton.Text = "خروج";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(80, 35);
            exitButton.Location = new Point(50, 300);
            exitButton.BackColor = Color.FromArgb(108, 117, 125);
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            // DataGridView
            stagesDataGridView = new DataGridView();
            stagesDataGridView.Location = new Point(300, 80);
            stagesDataGridView.Size = new Size(250, 350);
            stagesDataGridView.Font = new Font("Arial", 12);
            stagesDataGridView.BackgroundColor = Color.White;
            stagesDataGridView.BorderStyle = BorderStyle.FixedSingle;
            stagesDataGridView.AllowUserToAddRows = false;
            stagesDataGridView.AllowUserToDeleteRows = false;
            stagesDataGridView.ReadOnly = true;
            stagesDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            stagesDataGridView.MultiSelect = false;
            stagesDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            stagesDataGridView.SelectionChanged += StagesDataGridView_SelectionChanged;

            // Add column
            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "Name";
            nameColumn.HeaderText = "اسم المرحلة";
            nameColumn.Width = 200;
            stagesDataGridView.Columns.Add(nameColumn);

            this.Controls.Add(stagesDataGridView);

            this.ResumeLayout(false);
        }

        private void LoadStages()
        {
            try
            {
                if (File.Exists(stagesFilePath))
                {
                    stages = File.ReadAllLines(stagesFilePath).ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المراحل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveStages()
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(stagesFilePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                File.WriteAllLines(stagesFilePath, stages);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المراحل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            stagesDataGridView.Rows.Clear();
            foreach (string stage in stages)
            {
                stagesDataGridView.Rows.Add(stage);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(stageTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المرحلة!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (stages.Contains(stageTextBox.Text.Trim()))
            {
                MessageBox.Show("هذه المرحلة موجودة بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            stages.Add(stageTextBox.Text.Trim());
            SaveStages();
            RefreshDataGridView();
            stageTextBox.Clear();
            MessageBox.Show("تم حفظ المرحلة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (stagesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مرحلة للتعديل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(stageTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الجديد للمرحلة!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = stagesDataGridView.SelectedRows[0].Index;
            string oldName = stages[selectedIndex];
            string newName = stageTextBox.Text.Trim();

            if (stages.Contains(newName) && newName != oldName)
            {
                MessageBox.Show("هذه المرحلة موجودة بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            stages[selectedIndex] = newName;
            SaveStages();
            RefreshDataGridView();
            stageTextBox.Clear();
            MessageBox.Show("تم تعديل المرحلة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (stagesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مرحلة للحذف!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذه المرحلة؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                int selectedIndex = stagesDataGridView.SelectedRows[0].Index;
                stages.RemoveAt(selectedIndex);
                SaveStages();
                RefreshDataGridView();
                stageTextBox.Clear();
                MessageBox.Show("تم حذف المرحلة بنجاح!", "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void StagesDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (stagesDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = stagesDataGridView.SelectedRows[0].Index;
                if (selectedIndex < stages.Count)
                {
                    stageTextBox.Text = stages[selectedIndex];
                }
            }
        }
    }
}
