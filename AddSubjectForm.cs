using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class AddSubjectForm : Form
    {
        // مسار ملف حفظ التخصصات
        private readonly string subjectsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Subjects.txt");

        // Controls
        private TextBox subjectTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;
        private DataGridView subjectsDataGridView;
        private List<string> subjects;

        public AddSubjectForm()
        {
            subjects = new List<string>();
            InitializeComponent();
            LoadSubjects();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إضافة تخصص";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إضافة تخصص";
            titleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(500, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Left side - Input section
            CreateInputSection();

            // Right side - DataGridView
            CreateDataGridView();

            this.ResumeLayout(false);
        }

        private void CreateInputSection()
        {
            // Subject Label
            Label subjectLabel = new Label();
            subjectLabel.Text = "التخصص";
            subjectLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectLabel.Size = new Size(100, 25);
            subjectLabel.Location = new Point(30, 70);
            this.Controls.Add(subjectLabel);

            // Subject TextBox
            subjectTextBox = new TextBox();
            subjectTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectTextBox.Size = new Size(200, 30);
            subjectTextBox.Location = new Point(30, 100);
            this.Controls.Add(subjectTextBox);

            // Buttons - vertical layout
            CreateButtons();
        }

        private void CreateButtons()
        {
            // Save Button
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(200, 35);
            saveButton.Location = new Point(30, 130);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // Edit Button
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(200, 35);
            editButton.Location = new Point(30, 175);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Delete Button
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(200, 35);
            deleteButton.Location = new Point(30, 220);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // Close Button
            closeButton = new Button();
            closeButton.Text = "خروج من النافذة";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(200, 35);
            closeButton.Location = new Point(30, 265);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);
        }

        private void CreateDataGridView()
        {
            // DataGridView for subjects
            subjectsDataGridView = new DataGridView();
            subjectsDataGridView.Location = new Point(260, 70);
            subjectsDataGridView.Size = new Size(200, 230);
            subjectsDataGridView.BackgroundColor = Color.White;
            subjectsDataGridView.BorderStyle = BorderStyle.FixedSingle;
            subjectsDataGridView.AllowUserToAddRows = false;
            subjectsDataGridView.AllowUserToDeleteRows = false;
            subjectsDataGridView.ReadOnly = true;
            subjectsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            subjectsDataGridView.MultiSelect = false;
            subjectsDataGridView.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectsDataGridView.RowHeadersVisible = false;
            subjectsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            // Add column
            DataGridViewTextBoxColumn subjectColumn = new DataGridViewTextBoxColumn();
            subjectColumn.Name = "Subject";
            subjectColumn.HeaderText = "التخصص";
            subjectColumn.DataPropertyName = "Subject";
            subjectColumn.DefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectsDataGridView.Columns.Add(subjectColumn);

            // Set header font
            subjectsDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);

            // Event handlers
            subjectsDataGridView.SelectionChanged += SubjectsDataGridView_SelectionChanged;

            this.Controls.Add(subjectsDataGridView);
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                string newSubject = subjectTextBox.Text.Trim();
                
                if (subjects.Contains(newSubject))
                {
                    MessageBox.Show("هذا التخصص موجود مسبقاً!", "تخصص مكرر", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                subjects.Add(newSubject);
                SaveSubjects();
                RefreshDataGridView();
                subjectTextBox.Clear();
                
                MessageBox.Show("تم حفظ التخصص بنجاح!", "تأكيد الحفظ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (subjectsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تخصص للتعديل", "لم يتم اختيار تخصص", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            int selectedIndex = subjectsDataGridView.SelectedRows[0].Index;
            string newSubject = subjectTextBox.Text.Trim();
            
            if (subjects.Contains(newSubject) && subjects[selectedIndex] != newSubject)
            {
                MessageBox.Show("هذا التخصص موجود مسبقاً!", "تخصص مكرر", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            subjects[selectedIndex] = newSubject;
            SaveSubjects();
            RefreshDataGridView();
            subjectTextBox.Clear();
            
            MessageBox.Show("تم تعديل التخصص بنجاح!", "تأكيد التعديل", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (subjectsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تخصص للحذف", "لم يتم اختيار تخصص", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = subjectsDataGridView.SelectedRows[0].Index;
            string selectedSubject = subjects[selectedIndex];

            var result = MessageBox.Show($"هل أنت متأكد من حذف التخصص '{selectedSubject}'؟", 
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            
            if (result == DialogResult.Yes)
            {
                subjects.RemoveAt(selectedIndex);
                SaveSubjects();
                RefreshDataGridView();
                subjectTextBox.Clear();
                
                MessageBox.Show("تم حذف التخصص بنجاح!", "تأكيد الحذف", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void SubjectsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (subjectsDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = subjectsDataGridView.SelectedRows[0].Index;
                if (selectedIndex < subjects.Count)
                {
                    subjectTextBox.Text = subjects[selectedIndex];
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(subjectTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم التخصص", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                subjectTextBox.Focus();
                return false;
            }
            return true;
        }

        private void SaveSubjects()
        {
            try
            {
                string directory = Path.GetDirectoryName(subjectsFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllLines(subjectsFilePath, subjects);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ التخصصات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSubjects()
        {
            try
            {
                if (File.Exists(subjectsFilePath))
                {
                    subjects = File.ReadAllLines(subjectsFilePath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                }
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل التخصصات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            subjectsDataGridView.DataSource = null;
            subjectsDataGridView.DataSource = subjects.Select(s => new { Subject = s }).ToList();
        }
    }
}
