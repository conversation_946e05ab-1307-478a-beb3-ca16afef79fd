using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class SpecializationForm : Form
    {
        private TextBox specializationTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button exitButton;
        private DataGridView specializationsDataGridView;
        private List<string> specializations;

        private readonly string specializationsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Specializations.txt");

        public SpecializationForm()
        {
            specializations = new List<string>();
            InitializeComponent();
            LoadSpecializations();
            RefreshDataGridView();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "إدارة التخصصات";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إدارة التخصصات";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87);
            titleLabel.Size = new Size(500, 40);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // Input Section
            Label inputLabel = new Label();
            inputLabel.Text = "اسم التخصص:";
            inputLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            inputLabel.Size = new Size(100, 25);
            inputLabel.Location = new Point(50, 80);
            this.Controls.Add(inputLabel);

            specializationTextBox = new TextBox();
            specializationTextBox.Font = new Font("Arial", 12);
            specializationTextBox.Size = new Size(200, 25);
            specializationTextBox.Location = new Point(50, 110);
            this.Controls.Add(specializationTextBox);

            // Buttons
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(80, 35);
            saveButton.Location = new Point(50, 150);
            saveButton.BackColor = Color.FromArgb(40, 167, 69);
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(80, 35);
            editButton.Location = new Point(50, 200);
            editButton.BackColor = Color.FromArgb(255, 193, 7);
            editButton.ForeColor = Color.Black;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(80, 35);
            deleteButton.Location = new Point(50, 250);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69);
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            exitButton = new Button();
            exitButton.Text = "خروج";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(80, 35);
            exitButton.Location = new Point(50, 300);
            exitButton.BackColor = Color.FromArgb(108, 117, 125);
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            // DataGridView
            specializationsDataGridView = new DataGridView();
            specializationsDataGridView.Location = new Point(300, 80);
            specializationsDataGridView.Size = new Size(250, 350);
            specializationsDataGridView.Font = new Font("Arial", 12);
            specializationsDataGridView.BackgroundColor = Color.White;
            specializationsDataGridView.BorderStyle = BorderStyle.FixedSingle;
            specializationsDataGridView.AllowUserToAddRows = false;
            specializationsDataGridView.AllowUserToDeleteRows = false;
            specializationsDataGridView.ReadOnly = true;
            specializationsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            specializationsDataGridView.MultiSelect = false;
            specializationsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            specializationsDataGridView.SelectionChanged += SpecializationsDataGridView_SelectionChanged;

            // Add column
            DataGridViewTextBoxColumn nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "Name";
            nameColumn.HeaderText = "اسم التخصص";
            nameColumn.Width = 200;
            specializationsDataGridView.Columns.Add(nameColumn);

            this.Controls.Add(specializationsDataGridView);

            this.ResumeLayout(false);
        }

        private void LoadSpecializations()
        {
            try
            {
                if (File.Exists(specializationsFilePath))
                {
                    specializations = File.ReadAllLines(specializationsFilePath).ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التخصصات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveSpecializations()
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(specializationsFilePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                File.WriteAllLines(specializationsFilePath, specializations);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التخصصات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            specializationsDataGridView.Rows.Clear();
            foreach (string specialization in specializations)
            {
                specializationsDataGridView.Rows.Add(specialization);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(specializationTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم التخصص!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (specializations.Contains(specializationTextBox.Text.Trim()))
            {
                MessageBox.Show("هذا التخصص موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            specializations.Add(specializationTextBox.Text.Trim());
            SaveSpecializations();
            RefreshDataGridView();
            specializationTextBox.Clear();
            MessageBox.Show("تم حفظ التخصص بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (specializationsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تخصص للتعديل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(specializationTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الجديد للتخصص!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = specializationsDataGridView.SelectedRows[0].Index;
            string oldName = specializations[selectedIndex];
            string newName = specializationTextBox.Text.Trim();

            if (specializations.Contains(newName) && newName != oldName)
            {
                MessageBox.Show("هذا التخصص موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            specializations[selectedIndex] = newName;
            SaveSpecializations();
            RefreshDataGridView();
            specializationTextBox.Clear();
            MessageBox.Show("تم تعديل التخصص بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (specializationsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تخصص للحذف!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا التخصص؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                int selectedIndex = specializationsDataGridView.SelectedRows[0].Index;
                specializations.RemoveAt(selectedIndex);
                SaveSpecializations();
                RefreshDataGridView();
                specializationTextBox.Clear();
                MessageBox.Show("تم حذف التخصص بنجاح!", "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void SpecializationsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (specializationsDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = specializationsDataGridView.SelectedRows[0].Index;
                if (selectedIndex < specializations.Count)
                {
                    specializationTextBox.Text = specializations[selectedIndex];
                }
            }
        }
    }
}
