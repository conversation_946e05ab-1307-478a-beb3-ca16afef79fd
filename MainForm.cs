using System;
using System.Drawing;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class MainForm : Form
    {
        private AutoDistributionForm autoDistributionForm;

        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "منظم أعمال إدارة المدرسة";
            this.Size = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background

            // Title Label - Enhanced design
            Label titleLabel = new Label();
            titleLabel.Text = "منظم أعمال إدارة المدرسة";
            titleLabel.Font = new Font("Arial", 20, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87); // Dark gray
            titleLabel.Size = new Size(500, 50);
            titleLabel.Location = new Point(50, 40);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // Teachers Button
            Button teachersButton = new Button();
            teachersButton.Text = "إدارة بيانات المعلمين";
            teachersButton.Font = new Font("Arial", 12, FontStyle.Bold);
            teachersButton.Size = new Size(200, 50);
            teachersButton.Location = new Point(200, 130);
            teachersButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            teachersButton.ForeColor = Color.White;
            teachersButton.FlatStyle = FlatStyle.Flat;
            teachersButton.FlatAppearance.BorderSize = 0;
            teachersButton.Cursor = Cursors.Hand;
            teachersButton.Click += TeachersButton_Click;
            this.Controls.Add(teachersButton);

            // Auto Distribution Button
            Button autoDistributionButton = new Button();
            autoDistributionButton.Text = "التوزيع الآلي";
            autoDistributionButton.Font = new Font("Arial", 12, FontStyle.Bold);
            autoDistributionButton.Size = new Size(200, 50);
            autoDistributionButton.Location = new Point(200, 200);
            autoDistributionButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            autoDistributionButton.ForeColor = Color.White;
            autoDistributionButton.FlatStyle = FlatStyle.Flat;
            autoDistributionButton.FlatAppearance.BorderSize = 0;
            autoDistributionButton.Cursor = Cursors.Hand;
            autoDistributionButton.Click += AutoDistributionButton_Click;
            this.Controls.Add(autoDistributionButton);

            // Exit Button
            Button exitButton = new Button();
            exitButton.Text = "خروج";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(100, 40);
            exitButton.Location = new Point(250, 280);
            exitButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Cursor = Cursors.Hand;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            this.ResumeLayout(false);
        }

        private void TeachersButton_Click(object sender, EventArgs e)
        {
            // إنشاء نافذة التوزيع الآلي إذا لم تكن موجودة
            if (autoDistributionForm == null || autoDistributionForm.IsDisposed)
            {
                autoDistributionForm = new AutoDistributionForm();
            }

            TeachersForm teachersForm = new TeachersForm(autoDistributionForm);
            teachersForm.ShowDialog();
        }

        private void AutoDistributionButton_Click(object sender, EventArgs e)
        {
            // إنشاء نافذة التوزيع الآلي إذا لم تكن موجودة
            if (autoDistributionForm == null || autoDistributionForm.IsDisposed)
            {
                autoDistributionForm = new AutoDistributionForm();
            }

            autoDistributionForm.ShowDialog();
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
    }
}
