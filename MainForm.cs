using System;
using System.Drawing;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "منظم أعمال إدارة المدرسة";
            this.Size = new Size(600, 560);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background

            // Title Label - Enhanced design
            Label titleLabel = new Label();
            titleLabel.Text = "منظم أعمال إدارة المدرسة";
            titleLabel.Font = new Font("Arial", 20, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87); // Dark gray
            titleLabel.Size = new Size(500, 50);
            titleLabel.Location = new Point(50, 40);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);



            // Settings Button
            Button settingsButton = new Button();
            settingsButton.Text = "إعدادات البرنامج";
            settingsButton.Font = new Font("Arial", 12, FontStyle.Bold);
            settingsButton.Size = new Size(200, 50);
            settingsButton.Location = new Point(200, 130);
            settingsButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            settingsButton.ForeColor = Color.White;
            settingsButton.FlatStyle = FlatStyle.Flat;
            settingsButton.FlatAppearance.BorderSize = 0;
            settingsButton.Cursor = Cursors.Hand;
            settingsButton.Click += SettingsButton_Click;
            this.Controls.Add(settingsButton);

            // Data Entry Button
            Button dataEntryButton = new Button();
            dataEntryButton.Text = "إدخال البيانات";
            dataEntryButton.Font = new Font("Arial", 12, FontStyle.Bold);
            dataEntryButton.Size = new Size(200, 50);
            dataEntryButton.Location = new Point(200, 190);
            dataEntryButton.BackColor = Color.FromArgb(23, 162, 184); // Bootstrap info cyan
            dataEntryButton.ForeColor = Color.White;
            dataEntryButton.FlatStyle = FlatStyle.Flat;
            dataEntryButton.FlatAppearance.BorderSize = 0;
            dataEntryButton.Cursor = Cursors.Hand;
            dataEntryButton.Click += DataEntryButton_Click;
            this.Controls.Add(dataEntryButton);

            // View Employee Data Button
            Button viewEmployeeDataButton = new Button();
            viewEmployeeDataButton.Text = "عرض بيانات الموظفين";
            viewEmployeeDataButton.Font = new Font("Arial", 12, FontStyle.Bold);
            viewEmployeeDataButton.Size = new Size(200, 50);
            viewEmployeeDataButton.Location = new Point(200, 250);
            viewEmployeeDataButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning yellow
            viewEmployeeDataButton.ForeColor = Color.Black;
            viewEmployeeDataButton.FlatStyle = FlatStyle.Flat;
            viewEmployeeDataButton.FlatAppearance.BorderSize = 0;
            viewEmployeeDataButton.Cursor = Cursors.Hand;
            viewEmployeeDataButton.Click += ViewEmployeeDataButton_Click;
            this.Controls.Add(viewEmployeeDataButton);

            // Auto Distribution Button
            Button autoDistributionButton = new Button();
            autoDistributionButton.Text = "شاشة التوزيع الآلي";
            autoDistributionButton.Font = new Font("Arial", 12, FontStyle.Bold);
            autoDistributionButton.Size = new Size(200, 50);
            autoDistributionButton.Location = new Point(200, 310);
            autoDistributionButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            autoDistributionButton.ForeColor = Color.White;
            autoDistributionButton.FlatStyle = FlatStyle.Flat;
            autoDistributionButton.FlatAppearance.BorderSize = 0;
            autoDistributionButton.Cursor = Cursors.Hand;
            autoDistributionButton.Click += AutoDistributionButton_Click;
            this.Controls.Add(autoDistributionButton);

            // Exit Button
            Button exitButton = new Button();
            exitButton.Text = "خروج";
            exitButton.Font = new Font("Arial", 12, FontStyle.Bold);
            exitButton.Size = new Size(200, 50);
            exitButton.Location = new Point(200, 380);
            exitButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            exitButton.ForeColor = Color.White;
            exitButton.FlatStyle = FlatStyle.Flat;
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Cursor = Cursors.Hand;
            exitButton.Click += ExitButton_Click;
            this.Controls.Add(exitButton);

            this.ResumeLayout(false);
        }

        private void AutoDistributionButton_Click(object sender, EventArgs e)
        {
            AutoDistributionForm autoDistributionForm = new AutoDistributionForm();
            autoDistributionForm.ShowDialog();
        }

        private void SettingsButton_Click(object sender, EventArgs e)
        {
            SettingsForm settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
        }

        private void DataEntryButton_Click(object sender, EventArgs e)
        {
            DataEntryForm dataEntryForm = new DataEntryForm();
            dataEntryForm.ShowDialog();
        }

        private void ViewEmployeeDataButton_Click(object sender, EventArgs e)
        {
            ViewEmployeeDataForm viewEmployeeDataForm = new ViewEmployeeDataForm();
            viewEmployeeDataForm.ShowDialog();
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من الخروج من برنامج إدارة المدرسة؟",
                                       "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
    }
}
