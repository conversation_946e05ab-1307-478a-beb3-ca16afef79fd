using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class AutoDistributionForm : Form
    {
        private DataGridView distributionDataGridView;
        private TextBox nameSearchTextBox;
        private DateTimePicker specificDatePicker;
        private DateTimePicker fromDatePicker;
        private DateTimePicker toDatePicker;
        private Button clearSearchButton;
        private GroupBox actionsGroupBox;
        private Button autoDistributeButton;
        private Button clearFieldsButton;
        private Button redistributeButton;
        private Button reportButton;
        private DateTimePicker academicYearStartPicker;
        private DateTimePicker academicYearEndPicker;
        private List<DistributionRecord> distributionRecords;
        private List<DistributionRecord> filteredRecords;

        public static AutoDistributionForm Instance { get; private set; }

        public AutoDistributionForm()
        {
            distributionRecords = new List<DistributionRecord>();
            filteredRecords = new List<DistributionRecord>();
            Instance = this;
            InitializeComponent();
            LoadDistributionData();
            LoadAcademicYearDates();
            RefreshDataGridView();
        }

        private void InitializeComponent()
        {
            this.Text = "شاشة التوزيع الآلي";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Font = new Font("Arial", 12, FontStyle.Bold);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateSearchControls();
            CreateActionsGroupBox();
            CreateDataGridView();
        }

        private void CreateSearchControls()
        {
            // Search GroupBox
            GroupBox searchGroupBox = new GroupBox();
            searchGroupBox.Text = "البحث والتصفية";
            searchGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchGroupBox.Size = new Size(1150, 80);
            searchGroupBox.Location = new Point(20, 20);
            this.Controls.Add(searchGroupBox);

            // Search controls in horizontal row (Right to Left order)
            Label nameLabel = new Label();
            nameLabel.Text = "البحث بالاسم:";
            nameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            nameLabel.Size = new Size(100, 30);
            nameLabel.Location = new Point(1020, 30);
            searchGroupBox.Controls.Add(nameLabel);

            nameSearchTextBox = new TextBox();
            nameSearchTextBox.Font = new Font("Arial", 12);
            nameSearchTextBox.Size = new Size(150, 30);
            nameSearchTextBox.Location = new Point(860, 30);
            nameSearchTextBox.TextChanged += NameSearchTextBox_TextChanged;
            searchGroupBox.Controls.Add(nameSearchTextBox);

            Label specificDateLabel = new Label();
            specificDateLabel.Text = "تاريخ محدد:";
            specificDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            specificDateLabel.Size = new Size(80, 30);
            specificDateLabel.Location = new Point(770, 30);
            searchGroupBox.Controls.Add(specificDateLabel);

            specificDatePicker = new DateTimePicker();
            specificDatePicker.Font = new Font("Arial", 10);
            specificDatePicker.Size = new Size(120, 30);
            specificDatePicker.Location = new Point(640, 30);
            specificDatePicker.Format = DateTimePickerFormat.Short;
            specificDatePicker.ValueChanged += SpecificDatePicker_ValueChanged;
            searchGroupBox.Controls.Add(specificDatePicker);

            Label fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            fromDateLabel.Size = new Size(70, 30);
            fromDateLabel.Location = new Point(560, 30);
            searchGroupBox.Controls.Add(fromDateLabel);

            fromDatePicker = new DateTimePicker();
            fromDatePicker.Font = new Font("Arial", 10);
            fromDatePicker.Size = new Size(120, 30);
            fromDatePicker.Location = new Point(430, 30);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.ValueChanged += DateRangePicker_ValueChanged;
            searchGroupBox.Controls.Add(fromDatePicker);

            Label toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            toDateLabel.Size = new Size(70, 30);
            toDateLabel.Location = new Point(350, 30);
            searchGroupBox.Controls.Add(toDateLabel);

            toDatePicker = new DateTimePicker();
            toDatePicker.Font = new Font("Arial", 10);
            toDatePicker.Size = new Size(120, 30);
            toDatePicker.Location = new Point(220, 30);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.ValueChanged += DateRangePicker_ValueChanged;
            searchGroupBox.Controls.Add(toDatePicker);

            clearSearchButton = new Button();
            clearSearchButton.Text = "مسح البحث";
            clearSearchButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearSearchButton.Size = new Size(100, 35);
            clearSearchButton.Location = new Point(110, 27);
            clearSearchButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary
            clearSearchButton.ForeColor = Color.White;
            clearSearchButton.FlatStyle = FlatStyle.Flat;
            clearSearchButton.FlatAppearance.BorderSize = 0;
            clearSearchButton.Cursor = Cursors.Hand;
            clearSearchButton.Click += ClearSearchButton_Click;
            searchGroupBox.Controls.Add(clearSearchButton);
        }

        private void CreateActionsGroupBox()
        {
            actionsGroupBox = new GroupBox();
            actionsGroupBox.Text = "العمليات";
            actionsGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            actionsGroupBox.Size = new Size(1150, 120);
            actionsGroupBox.Location = new Point(20, 120);
            this.Controls.Add(actionsGroupBox);

            // Academic Year Dates
            Label academicYearStartLabel = new Label();
            academicYearStartLabel.Text = "تاريخ بداية العام الدراسي:";
            academicYearStartLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            academicYearStartLabel.Size = new Size(180, 30);
            academicYearStartLabel.Location = new Point(950, 25);
            actionsGroupBox.Controls.Add(academicYearStartLabel);

            academicYearStartPicker = new DateTimePicker();
            academicYearStartPicker.Font = new Font("Arial", 10);
            academicYearStartPicker.Size = new Size(120, 30);
            academicYearStartPicker.Location = new Point(820, 25);
            academicYearStartPicker.Format = DateTimePickerFormat.Short;
            academicYearStartPicker.Value = new DateTime(DateTime.Now.Year, 9, 1); // Default to September 1st
            academicYearStartPicker.ValueChanged += AcademicYearStartPicker_ValueChanged;
            actionsGroupBox.Controls.Add(academicYearStartPicker);

            Label academicYearEndLabel = new Label();
            academicYearEndLabel.Text = "تاريخ نهاية العام الدراسي:";
            academicYearEndLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            academicYearEndLabel.Size = new Size(180, 30);
            academicYearEndLabel.Location = new Point(620, 25);
            actionsGroupBox.Controls.Add(academicYearEndLabel);

            academicYearEndPicker = new DateTimePicker();
            academicYearEndPicker.Font = new Font("Arial", 10);
            academicYearEndPicker.Size = new Size(120, 30);
            academicYearEndPicker.Location = new Point(490, 25);
            academicYearEndPicker.Format = DateTimePickerFormat.Short;
            academicYearEndPicker.Value = new DateTime(DateTime.Now.Year + 1, 6, 30); // Default to June 30th next year
            academicYearEndPicker.ValueChanged += AcademicYearEndPicker_ValueChanged;
            actionsGroupBox.Controls.Add(academicYearEndPicker);

            autoDistributeButton = new Button();
            autoDistributeButton.Text = "التوزيع الآلي";
            autoDistributeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            autoDistributeButton.Size = new Size(120, 40);
            autoDistributeButton.Location = new Point(1000, 70);
            autoDistributeButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success
            autoDistributeButton.ForeColor = Color.White;
            autoDistributeButton.FlatStyle = FlatStyle.Flat;
            autoDistributeButton.FlatAppearance.BorderSize = 0;
            autoDistributeButton.Cursor = Cursors.Hand;
            autoDistributeButton.Click += AutoDistributeButton_Click;
            actionsGroupBox.Controls.Add(autoDistributeButton);

            clearFieldsButton = new Button();
            clearFieldsButton.Text = "مسح الحقول";
            clearFieldsButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearFieldsButton.Size = new Size(120, 40);
            clearFieldsButton.Location = new Point(860, 70);
            clearFieldsButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning
            clearFieldsButton.ForeColor = Color.Black;
            clearFieldsButton.FlatStyle = FlatStyle.Flat;
            clearFieldsButton.FlatAppearance.BorderSize = 0;
            clearFieldsButton.Cursor = Cursors.Hand;
            clearFieldsButton.Click += ClearFieldsButton_Click;
            actionsGroupBox.Controls.Add(clearFieldsButton);

            redistributeButton = new Button();
            redistributeButton.Text = "إعادة التوزيع الآلي";
            redistributeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            redistributeButton.Size = new Size(140, 40);
            redistributeButton.Location = new Point(700, 70);
            redistributeButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning
            redistributeButton.ForeColor = Color.Black;
            redistributeButton.FlatStyle = FlatStyle.Flat;
            redistributeButton.FlatAppearance.BorderSize = 0;
            redistributeButton.Cursor = Cursors.Hand;
            redistributeButton.Click += RedistributeButton_Click;
            actionsGroupBox.Controls.Add(redistributeButton);

            reportButton = new Button();
            reportButton.Text = "التقرير";
            reportButton.Font = new Font("Arial", 12, FontStyle.Bold);
            reportButton.Size = new Size(120, 40);
            reportButton.Location = new Point(560, 70);
            reportButton.BackColor = Color.FromArgb(23, 162, 184); // Bootstrap info
            reportButton.ForeColor = Color.White;
            reportButton.FlatStyle = FlatStyle.Flat;
            reportButton.FlatAppearance.BorderSize = 0;
            reportButton.Cursor = Cursors.Hand;
            reportButton.Click += ReportButton_Click;
            actionsGroupBox.Controls.Add(reportButton);
        }

        private void CreateDataGridView()
        {
            distributionDataGridView = new DataGridView();
            distributionDataGridView.Location = new Point(20, 260);
            distributionDataGridView.Size = new Size(1150, 390);
            distributionDataGridView.Font = new Font("Arial", 10);
            distributionDataGridView.BackgroundColor = Color.White;
            distributionDataGridView.BorderStyle = BorderStyle.Fixed3D;
            distributionDataGridView.AllowUserToAddRows = false;
            distributionDataGridView.AllowUserToDeleteRows = false;
            distributionDataGridView.ReadOnly = true;
            distributionDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            distributionDataGridView.MultiSelect = false;
            distributionDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            distributionDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            distributionDataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            distributionDataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            distributionDataGridView.ColumnHeadersHeight = 40;
            distributionDataGridView.RowTemplate.Height = 35;

            // Add columns
            distributionDataGridView.Columns.Add("TeacherName", "اسم المعلم");
            distributionDataGridView.Columns.Add("Preparation1", "تحضير1");
            distributionDataGridView.Columns.Add("Homework1", "واجبات1");
            distributionDataGridView.Columns.Add("Visit1", "زيارة1");
            distributionDataGridView.Columns.Add("Preparation2", "تحضير2");
            distributionDataGridView.Columns.Add("Homework2", "واجبات2");
            distributionDataGridView.Columns.Add("Visit2", "زيارة2");
            distributionDataGridView.Columns.Add("Supervisor", "مسؤول المتابعة");

            // Set column widths
            distributionDataGridView.Columns["TeacherName"].FillWeight = 20;
            distributionDataGridView.Columns["Preparation1"].FillWeight = 12;
            distributionDataGridView.Columns["Homework1"].FillWeight = 12;
            distributionDataGridView.Columns["Visit1"].FillWeight = 12;
            distributionDataGridView.Columns["Preparation2"].FillWeight = 12;
            distributionDataGridView.Columns["Homework2"].FillWeight = 12;
            distributionDataGridView.Columns["Visit2"].FillWeight = 12;
            distributionDataGridView.Columns["Supervisor"].FillWeight = 18;

            this.Controls.Add(distributionDataGridView);
        }

        private void LoadDistributionData()
        {
            try
            {
                string distributionFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                                                         "SchoolManagement", "distribution.txt");

                distributionRecords.Clear();

                if (File.Exists(distributionFilePath))
                {
                    var lines = File.ReadAllLines(distributionFilePath);
                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            var parts = line.Split('|');
                            if (parts.Length >= 8)
                            {
                                var record = new DistributionRecord
                                {
                                    TeacherName = parts[0],
                                    Preparation1 = parts[1],
                                    Homework1 = parts[2],
                                    Visit1 = parts[3],
                                    Preparation2 = parts[4],
                                    Homework2 = parts[5],
                                    Visit2 = parts[6],
                                    Supervisor = parts[7]
                                };
                                distributionRecords.Add(record);
                            }
                        }
                    }
                }

                filteredRecords = new List<DistributionRecord>(distributionRecords);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات التوزيع: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void RefreshDataGridView()
        {
            distributionDataGridView.Rows.Clear();

            foreach (var record in filteredRecords)
            {
                distributionDataGridView.Rows.Add(
                    record.TeacherName,
                    record.Preparation1,
                    record.Homework1,
                    record.Visit1,
                    record.Preparation2,
                    record.Homework2,
                    record.Visit2,
                    record.Supervisor
                );
            }

            // Apply date highlighting
            ApplyDateHighlighting();

            // Force the grid to update immediately
            distributionDataGridView.Refresh();
            this.Refresh();
        }

        private void ApplyDateHighlighting()
        {
            DateTime today = DateTime.Today;
            DateTime tomorrow = today.AddDays(1);

            for (int i = 0; i < distributionDataGridView.Rows.Count; i++)
            {
                for (int j = 1; j <= 6; j++) // Date columns (1-6)
                {
                    var cellValue = distributionDataGridView.Rows[i].Cells[j].Value?.ToString();
                    if (!string.IsNullOrEmpty(cellValue) && DateTime.TryParse(cellValue, out DateTime cellDate))
                    {
                        if (cellDate.Date == today)
                        {
                            distributionDataGridView.Rows[i].Cells[j].Style.BackColor = Color.LightGreen;
                        }
                        else if (cellDate.Date == tomorrow)
                        {
                            distributionDataGridView.Rows[i].Cells[j].Style.BackColor = Color.LightSkyBlue;
                        }
                    }
                }
            }
        }

        private void NameSearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SpecificDatePicker_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DateRangePicker_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            filteredRecords = new List<DistributionRecord>(distributionRecords);

            // Filter by name
            if (!string.IsNullOrWhiteSpace(nameSearchTextBox.Text))
            {
                string searchName = nameSearchTextBox.Text.Trim().ToLower();
                filteredRecords = filteredRecords.Where(r => r.TeacherName.ToLower().Contains(searchName)).ToList();
            }

            // Filter by specific date
            if (specificDatePicker.Checked)
            {
                string searchDate = specificDatePicker.Value.ToString("yyyy/MM/dd");
                filteredRecords = filteredRecords.Where(r =>
                    r.Preparation1 == searchDate || r.Homework1 == searchDate || r.Visit1 == searchDate ||
                    r.Preparation2 == searchDate || r.Homework2 == searchDate || r.Visit2 == searchDate
                ).ToList();
            }

            // Filter by date range
            if (fromDatePicker.Checked && toDatePicker.Checked)
            {
                DateTime fromDate = fromDatePicker.Value.Date;
                DateTime toDate = toDatePicker.Value.Date;

                filteredRecords = filteredRecords.Where(r =>
                {
                    var dates = new[] { r.Preparation1, r.Homework1, r.Visit1, r.Preparation2, r.Homework2, r.Visit2 };
                    return dates.Any(dateStr =>
                    {
                        if (DateTime.TryParse(dateStr, out DateTime date))
                        {
                            return date.Date >= fromDate && date.Date <= toDate;
                        }
                        return false;
                    });
                }).ToList();
            }

            RefreshDataGridView();
        }

        private void AcademicYearStartPicker_ValueChanged(object sender, EventArgs e)
        {
            SaveAcademicYearDates();
        }

        private void AcademicYearEndPicker_ValueChanged(object sender, EventArgs e)
        {
            SaveAcademicYearDates();
        }

        private void ClearSearchButton_Click(object sender, EventArgs e)
        {
            nameSearchTextBox.Clear();
            specificDatePicker.Checked = false;
            fromDatePicker.Checked = false;
            toDatePicker.Checked = false;
            filteredRecords = new List<DistributionRecord>(distributionRecords);
            RefreshDataGridView();
        }

        private void LoadAcademicYearDates()
        {
            try
            {
                string academicYearFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                                                         "SchoolManagement", "academic_year.txt");

                if (File.Exists(academicYearFilePath))
                {
                    var lines = File.ReadAllLines(academicYearFilePath);
                    if (lines.Length >= 2)
                    {
                        if (DateTime.TryParse(lines[0], out DateTime startDate))
                            academicYearStartPicker.Value = startDate;
                        if (DateTime.TryParse(lines[1], out DateTime endDate))
                            academicYearEndPicker.Value = endDate;
                    }
                    else
                    {
                        // إذا كان الملف موجود لكن لا يحتوي على بيانات كافية، احفظ القيم الافتراضية
                        SaveAcademicYearDates();
                    }
                }
                else
                {
                    // إذا لم يكن الملف موجود، احفظ القيم الافتراضية
                    SaveAcademicYearDates();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل تواريخ العام الدراسي: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveAcademicYearDates()
        {
            try
            {
                string directoryPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SchoolManagement");
                if (!Directory.Exists(directoryPath))
                    Directory.CreateDirectory(directoryPath);

                string academicYearFilePath = Path.Combine(directoryPath, "academic_year.txt");
                var lines = new[]
                {
                    academicYearStartPicker.Value.ToString("yyyy/MM/dd"),
                    academicYearEndPicker.Value.ToString("yyyy/MM/dd")
                };
                File.WriteAllLines(academicYearFilePath, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ تواريخ العام الدراسي: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AutoDistributeButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("وظيفة التوزيع الآلي غير متاحة حالياً.", "غير متاح",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ClearFieldsButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من مسح جميع الحقول؟\nسيتم حذف جميع بيانات التوزيع!",
                                       "تأكيد المسح", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                distributionRecords.Clear();
                filteredRecords.Clear();
                RefreshDataGridView();

                // Clear the file
                try
                {
                    string distributionFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                                                             "SchoolManagement", "distribution.txt");
                    if (File.Exists(distributionFilePath))
                    {
                        File.WriteAllText(distributionFilePath, "");
                    }

                    MessageBox.Show("تم مسح جميع الحقول بنجاح!", "تم المسح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء مسح الملف: {ex.Message}", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }



        private DateTime GenerateVariedStartDate(DateTime baseStartDate, List<DateTime> existingDates, Random random)
        {
            // Generate a start date with more variation between employees
            int maxVariation = Math.Min(30, (int)(baseStartDate.AddDays(60) - baseStartDate).TotalDays);
            DateTime candidateDate = baseStartDate.AddDays(random.Next(0, maxVariation));

            // Try to avoid dates that are too close to existing dates
            int attempts = 0;
            while (attempts < 10 && existingDates.Any(d => Math.Abs((d - candidateDate).TotalDays) < 3))
            {
                candidateDate = baseStartDate.AddDays(random.Next(0, maxVariation));
                attempts++;
            }

            return candidateDate;
        }

        private DateTime EnsureDateVariety(DateTime proposedDate, List<DateTime> existingDates, Random random)
        {
            // Check if the proposed date is too close to existing dates
            while (existingDates.Any(d => Math.Abs((d - proposedDate).TotalDays) < 2))
            {
                // Adjust the date by 1-3 days to create variety
                int adjustment = random.Next(1, 4);
                if (random.Next(2) == 0)
                    proposedDate = proposedDate.AddDays(adjustment);
                else
                    proposedDate = proposedDate.AddDays(-adjustment);

                // Skip weekends
                while (proposedDate.DayOfWeek == DayOfWeek.Friday || proposedDate.DayOfWeek == DayOfWeek.Saturday)
                {
                    proposedDate = proposedDate.AddDays(1);
                }
            }

            return proposedDate;
        }

        private void RedistributeButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("وظيفة إعادة التوزيع الآلي غير متاحة حالياً.", "غير متاح",
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ReportButton_Click(object sender, EventArgs e)
        {
            if (filteredRecords.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات لعرضها في التقرير!", "لا توجد بيانات",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            GenerateReport();
        }

        private void GenerateReport()
        {
            try
            {
                string reportContent = CreateReportContent();
                var reportForm = new ReportForm(reportContent);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string CreateReportContent()
        {
            var report = new System.Text.StringBuilder();

            // Report Header
            report.AppendLine("<!DOCTYPE html>");
            report.AppendLine("<html dir='rtl'>");
            report.AppendLine("<head>");
            report.AppendLine("<meta charset='UTF-8'>");
            report.AppendLine("<title>تقرير التوزيع الآلي</title>");
            report.AppendLine("<style>");
            report.AppendLine("body { font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }");
            report.AppendLine("h1 { text-align: center; color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }");
            report.AppendLine("h2 { color: #34495e; margin-top: 30px; }");
            report.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }");
            report.AppendLine("th { background-color: #34495e; color: white; padding: 12px; text-align: center; font-weight: bold; }");
            report.AppendLine("td { padding: 10px; text-align: center; border-bottom: 1px solid #ddd; }");
            report.AppendLine("tr:nth-child(even) { background-color: #f8f9fa; }");
            report.AppendLine("tr:hover { background-color: #e8f4f8; }");
            report.AppendLine(".today { background-color: #d4edda !important; font-weight: bold; }");
            report.AppendLine(".tomorrow { background-color: #cce7ff !important; font-weight: bold; }");
            report.AppendLine(".info-section { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }");
            report.AppendLine(".summary { display: flex; justify-content: space-around; margin: 20px 0; }");
            report.AppendLine(".summary-item { text-align: center; padding: 15px; background-color: #3498db; color: white; border-radius: 5px; min-width: 150px; }");
            report.AppendLine("</style>");
            report.AppendLine("</head>");
            report.AppendLine("<body>");

            // Title
            report.AppendLine("<h1>تقرير التوزيع الآلي لمتابعة المعلمين</h1>");

            // Report Info
            report.AppendLine("<div class='info-section'>");
            report.AppendLine($"<p><strong>تاريخ التقرير:</strong> {DateTime.Now:yyyy/MM/dd - HH:mm}</p>");
            report.AppendLine($"<p><strong>عدد المعلمين:</strong> {filteredRecords.Count}</p>");
            report.AppendLine($"<p><strong>إجمالي المتابعات:</strong> {filteredRecords.Count * 6}</p>");
            report.AppendLine("</div>");

            // Summary Statistics
            var todayCount = CountTodayFollowUps();
            var tomorrowCount = CountTomorrowFollowUps();
            var totalFollowUps = filteredRecords.Count * 6;

            report.AppendLine("<div class='summary'>");
            report.AppendLine($"<div class='summary-item'><h3>{totalFollowUps}</h3><p>إجمالي المتابعات</p></div>");
            report.AppendLine($"<div class='summary-item' style='background-color: #27ae60;'><h3>{todayCount}</h3><p>متابعات اليوم</p></div>");
            report.AppendLine($"<div class='summary-item' style='background-color: #3498db;'><h3>{tomorrowCount}</h3><p>متابعات الغد</p></div>");
            report.AppendLine("</div>");

            // Data Table
            report.AppendLine("<h2>بيانات التوزيع التفصيلية</h2>");
            report.AppendLine("<table>");
            report.AppendLine("<thead>");
            report.AppendLine("<tr>");
            report.AppendLine("<th>اسم المعلم</th>");
            report.AppendLine("<th>تحضير1</th>");
            report.AppendLine("<th>واجبات1</th>");
            report.AppendLine("<th>زيارة1</th>");
            report.AppendLine("<th>تحضير2</th>");
            report.AppendLine("<th>واجبات2</th>");
            report.AppendLine("<th>زيارة2</th>");
            report.AppendLine("<th>مسؤول المتابعة</th>");
            report.AppendLine("</tr>");
            report.AppendLine("</thead>");
            report.AppendLine("<tbody>");

            DateTime today = DateTime.Today;
            DateTime tomorrow = today.AddDays(1);

            foreach (var record in filteredRecords)
            {
                report.AppendLine("<tr>");
                report.AppendLine($"<td><strong>{record.TeacherName}</strong></td>");

                // Add date cells with highlighting
                var dates = new[] { record.Preparation1, record.Homework1, record.Visit1,
                                  record.Preparation2, record.Homework2, record.Visit2 };

                foreach (var dateStr in dates)
                {
                    string cssClass = "";
                    if (!string.IsNullOrEmpty(dateStr) && DateTime.TryParse(dateStr, out DateTime date))
                    {
                        if (date.Date == today)
                            cssClass = " class='today'";
                        else if (date.Date == tomorrow)
                            cssClass = " class='tomorrow'";
                    }
                    report.AppendLine($"<td{cssClass}>{dateStr}</td>");
                }

                report.AppendLine($"<td>{record.Supervisor}</td>");
                report.AppendLine("</tr>");
            }

            report.AppendLine("</tbody>");
            report.AppendLine("</table>");

            // Footer
            report.AppendLine("<div class='info-section' style='margin-top: 30px;'>");
            report.AppendLine("<p><strong>ملاحظات:</strong></p>");
            report.AppendLine("<ul>");
            report.AppendLine("<li>التواريخ المميزة باللون الأخضر تمثل متابعات اليوم</li>");
            report.AppendLine("<li>التواريخ المميزة باللون الأزرق تمثل متابعات الغد</li>");
            report.AppendLine("<li>يتم توزيع 6 متابعات لكل معلم (تحضير1، واجبات1، زيارة1، تحضير2، واجبات2، زيارة2)</li>");
            report.AppendLine("</ul>");
            report.AppendLine("</div>");

            report.AppendLine("</body>");
            report.AppendLine("</html>");

            return report.ToString();
        }

        private int CountTodayFollowUps()
        {
            DateTime today = DateTime.Today;
            int count = 0;

            foreach (var record in filteredRecords)
            {
                var dates = new[] { record.Preparation1, record.Homework1, record.Visit1,
                                  record.Preparation2, record.Homework2, record.Visit2 };

                foreach (var dateStr in dates)
                {
                    if (!string.IsNullOrEmpty(dateStr) && DateTime.TryParse(dateStr, out DateTime date))
                    {
                        if (date.Date == today)
                            count++;
                    }
                }
            }

            return count;
        }

        private int CountTomorrowFollowUps()
        {
            DateTime tomorrow = DateTime.Today.AddDays(1);
            int count = 0;

            foreach (var record in filteredRecords)
            {
                var dates = new[] { record.Preparation1, record.Homework1, record.Visit1,
                                  record.Preparation2, record.Homework2, record.Visit2 };

                foreach (var dateStr in dates)
                {
                    if (!string.IsNullOrEmpty(dateStr) && DateTime.TryParse(dateStr, out DateTime date))
                    {
                        if (date.Date == tomorrow)
                            count++;
                    }
                }
            }

            return count;
        }

        private List<Employee> LoadEmployeesFromFile()
        {
            var employees = new List<Employee>();
            try
            {
                string employeesFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                                                       "SchoolManagement", "employees.txt");

                if (File.Exists(employeesFilePath))
                {
                    var lines = File.ReadAllLines(employeesFilePath);
                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            var parts = line.Split('|');
                            if (parts.Length >= 7)
                            {
                                var employee = new Employee
                                {
                                    Name = parts[0],
                                    CivilId = parts[1],
                                    Subject = parts[2],
                                    Work = parts[3],
                                    Stage = parts[4],
                                    ClassesCount = parts[5],
                                    Mobile = parts[6],
                                    Supervisor = parts.Length > 7 ? parts[7] : ""
                                };
                                employees.Add(employee);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            return employees;
        }

        private List<string> LoadSupervisorsFromFile()
        {
            var supervisors = new List<string>();
            try
            {
                string supervisorsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                                                         "SchoolManagement", "supervisors.txt");

                if (File.Exists(supervisorsFilePath))
                {
                    var lines = File.ReadAllLines(supervisorsFilePath);
                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            supervisors.Add(line.Trim());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات المشرفين: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            return supervisors;
        }

        private void SaveDistributionData()
        {
            try
            {
                string directoryPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SchoolManagement");
                if (!Directory.Exists(directoryPath))
                    Directory.CreateDirectory(directoryPath);

                string distributionFilePath = Path.Combine(directoryPath, "distribution.txt");
                var lines = new List<string>();

                foreach (var record in distributionRecords)
                {
                    var line = $"{record.TeacherName}|{record.Preparation1}|{record.Homework1}|{record.Visit1}|{record.Preparation2}|{record.Homework2}|{record.Visit2}|{record.Supervisor}";
                    lines.Add(line);
                }

                File.WriteAllLines(distributionFilePath, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ بيانات التوزيع: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void AddNewTeacherDistribution(string teacherName, string supervisor)
        {
            if (Instance != null)
            {
                Instance.AddSingleTeacherDistribution(teacherName, supervisor);

                // Show the distribution form if it's not visible
                if (Instance.WindowState == FormWindowState.Minimized)
                {
                    Instance.WindowState = FormWindowState.Normal;
                }
                Instance.BringToFront();
                Instance.Focus();

                // Ensure the data is immediately visible
                Instance.RefreshDataGridView();
            }
            else
            {
                // Create new instance if not exists
                var distributionForm = new AutoDistributionForm();
                distributionForm.Show();
                distributionForm.AddSingleTeacherDistribution(teacherName, supervisor);
                distributionForm.RefreshDataGridView();
            }
        }

        public static void UpdateTeacherDistribution(string oldTeacherName, string newTeacherName, string newSupervisor)
        {
            if (Instance != null)
            {
                Instance.UpdateSingleTeacherDistribution(oldTeacherName, newTeacherName, newSupervisor);
            }
        }

        public static void RemoveTeacherDistribution(string teacherName)
        {
            if (Instance != null)
            {
                Instance.RemoveSingleTeacherDistribution(teacherName);
            }
        }

        private void AddSingleTeacherDistribution(string teacherName, string supervisor)
        {
            try
            {
                // Check if teacher already exists in distribution
                var existingRecord = distributionRecords.FirstOrDefault(r => r.TeacherName == teacherName);
                if (existingRecord != null)
                {
                    // Update supervisor if teacher already exists
                    existingRecord.Supervisor = supervisor;
                    SaveDistributionData();
                    filteredRecords = new List<DistributionRecord>(distributionRecords);

                    // Force immediate refresh of the grid
                    RefreshDataGridView();

                    // Ensure the form is visible and focused
                    this.Show();
                    this.BringToFront();
                    this.Focus();

                    // Find and highlight the updated record
                    for (int i = 0; i < distributionDataGridView.Rows.Count; i++)
                    {
                        if (distributionDataGridView.Rows[i].Cells[0].Value?.ToString() == teacherName)
                        {
                            distributionDataGridView.FirstDisplayedScrollingRowIndex = i;
                            distributionDataGridView.Rows[i].Selected = true;
                            break;
                        }
                    }

                    // Show update message
                    MessageBox.Show($"تم تحديث مسؤول المتابعة للمعلم '{teacherName}' في جدول التوزيع!\nمسؤول المتابعة الجديد: {supervisor}",
                                  "تم تحديث المعلم في التوزيع", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                DateTime startDate = academicYearStartPicker.Value.AddDays(20);
                DateTime endDate = academicYearEndPicker.Value.AddDays(-20);

                Random random = new Random();
                DateTime currentDate = startDate.AddDays(random.Next(0, 15));

                var record = new DistributionRecord
                {
                    TeacherName = teacherName,
                    Supervisor = supervisor
                };

                var dates = new List<DateTime>();

                for (int i = 0; i < 6; i++)
                {
                    while (currentDate.DayOfWeek == DayOfWeek.Friday || currentDate.DayOfWeek == DayOfWeek.Saturday)
                    {
                        currentDate = currentDate.AddDays(1);
                    }

                    if (currentDate <= endDate)
                    {
                        dates.Add(currentDate);
                        int interval = random.Next(20, 51);
                        currentDate = currentDate.AddDays(interval);
                    }
                    else
                    {
                        break;
                    }
                }

                if (dates.Count > 0) record.Preparation1 = dates[0].ToString("yyyy/MM/dd");
                if (dates.Count > 1) record.Homework1 = dates[1].ToString("yyyy/MM/dd");
                if (dates.Count > 2) record.Visit1 = dates[2].ToString("yyyy/MM/dd");
                if (dates.Count > 3) record.Preparation2 = dates[3].ToString("yyyy/MM/dd");
                if (dates.Count > 4) record.Homework2 = dates[4].ToString("yyyy/MM/dd");
                if (dates.Count > 5) record.Visit2 = dates[5].ToString("yyyy/MM/dd");

                distributionRecords.Add(record);
                SaveDistributionData();
                filteredRecords = new List<DistributionRecord>(distributionRecords);

                // Force immediate refresh of the grid
                RefreshDataGridView();

                // Ensure the form is visible and focused
                this.Show();
                this.BringToFront();
                this.Focus();

                // Scroll to the newly added record
                if (distributionDataGridView.Rows.Count > 0)
                {
                    int lastRowIndex = distributionDataGridView.Rows.Count - 1;
                    distributionDataGridView.FirstDisplayedScrollingRowIndex = lastRowIndex;
                    distributionDataGridView.Rows[lastRowIndex].Selected = true;
                }

                // Show success message
                MessageBox.Show($"تم إضافة المعلم '{teacherName}' إلى جدول التوزيع الآلي بنجاح!\nمسؤول المتابعة: {supervisor}",
                              "تم إضافة المعلم للتوزيع", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة توزيع المعلم الجديد: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateSingleTeacherDistribution(string oldTeacherName, string newTeacherName, string newSupervisor)
        {
            try
            {
                // Find the teacher record in distribution
                var record = distributionRecords.FirstOrDefault(r => r.TeacherName == oldTeacherName);
                if (record != null)
                {
                    // Update the teacher name and supervisor
                    record.TeacherName = newTeacherName;
                    record.Supervisor = newSupervisor;

                    SaveDistributionData();
                    filteredRecords = new List<DistributionRecord>(distributionRecords);
                    RefreshDataGridView();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث توزيع المعلم: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RemoveSingleTeacherDistribution(string teacherName)
        {
            try
            {
                // Find and remove the teacher record from distribution
                var record = distributionRecords.FirstOrDefault(r => r.TeacherName == teacherName);
                if (record != null)
                {
                    distributionRecords.Remove(record);
                    SaveDistributionData();
                    filteredRecords = new List<DistributionRecord>(distributionRecords);
                    RefreshDataGridView();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف توزيع المعلم: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public class Employee
        {
            public string Name { get; set; } = "";
            public string CivilId { get; set; } = "";
            public string Subject { get; set; } = "";
            public string Work { get; set; } = "";
            public string Stage { get; set; } = "";
            public string ClassesCount { get; set; } = "";
            public string Mobile { get; set; } = "";
            public string Supervisor { get; set; } = "";
        }

        public class DistributionRecord
        {
            public string TeacherName { get; set; } = "";
            public string Preparation1 { get; set; } = "";
            public string Homework1 { get; set; } = "";
            public string Visit1 { get; set; } = "";
            public string Preparation2 { get; set; } = "";
            public string Homework2 { get; set; } = "";
            public string Visit2 { get; set; } = "";
            public string Supervisor { get; set; } = "";
        }
    }
}
