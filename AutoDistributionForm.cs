using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class AutoDistributionForm : Form
    {
        private GroupBox operationsGroupBox;
        private GroupBox searchGroupBox;
        private GroupBox academicYearGroupBox;
        private DataGridView distributionDataGridView;
        private Button autoDistributeButton;
        private Button clearAllButton;
        private Button redistributeButton;
        private TextBox searchNameTextBox;
        private DateTimePicker searchDatePicker;
        private DateTimePicker startDatePicker;
        private DateTimePicker endDatePicker;
        private ComboBox supervisorComboBox;
        private ComboBox executionLevelSearchComboBox;
        private Button searchButton;
        private Button clearSearchButton;
        private Button reportButton;
        private DateTimePicker academicYearStartPicker;
        private DateTimePicker academicYearEndPicker;
        private Label recordCountLabel;

        // مسار ملف حفظ إعدادات العام الدراسي
        private readonly string settingsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "AcademicYearSettings.txt");

        private List<DistributionRecord> distributionRecords;

        public AutoDistributionForm()
        {
            distributionRecords = new List<DistributionRecord>();
            InitializeComponent();
            LoadTeachersFromFile(); // تحميل المعلمين من نافذة بيانات المعلمين
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "التوزيع الآلي للمهام";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;

            CreateOperationsSection();
            CreateAcademicYearSection();
            CreateSearchSection();
            CreateDataGridView();
            CreateRecordCountLabel();

            this.ResumeLayout(false);
            LoadAcademicYearSettings(); // تحميل إعدادات العام الدراسي المحفوظة
        }

        private void CreateOperationsSection()
        {
            // Operations GroupBox - CSS-like styling - reduced size
            operationsGroupBox = new GroupBox();
            operationsGroupBox.Text = "العمليات";
            operationsGroupBox.Location = new Point(20, 20);
            operationsGroupBox.Size = new Size(750, 80); // Reduced from 1150 to 750
            operationsGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            operationsGroupBox.RightToLeft = RightToLeft.Yes;
            operationsGroupBox.ForeColor = Color.DarkBlue;
            operationsGroupBox.BackColor = Color.FromArgb(248, 249, 250);
            operationsGroupBox.FlatStyle = FlatStyle.Flat;
            this.Controls.Add(operationsGroupBox);

            // CSS-like button container calculations for 4 buttons
            int buttonWidth = 150;
            int buttonHeight = 35;
            int buttonSpacing = 30;
            int totalButtonsWidth = (buttonWidth * 4) + (buttonSpacing * 3); // 4 buttons + 3 gaps
            int containerWidth = operationsGroupBox.Width - 40;
            int startX = (containerWidth - totalButtonsWidth) / 2 + 20;
            int buttonY = (operationsGroupBox.Height - buttonHeight) / 2 - 5;

            // Auto Distribute Button
            autoDistributeButton = new Button();
            autoDistributeButton.Text = "التوزيع الآلي";
            autoDistributeButton.Location = new Point(startX + (buttonWidth + buttonSpacing) * 2, buttonY);
            autoDistributeButton.Size = new Size(buttonWidth, buttonHeight);
            autoDistributeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            autoDistributeButton.BackColor = Color.FromArgb(40, 167, 69);
            autoDistributeButton.ForeColor = Color.White;
            autoDistributeButton.FlatStyle = FlatStyle.Flat;
            autoDistributeButton.FlatAppearance.BorderSize = 0;
            autoDistributeButton.Cursor = Cursors.Hand;
            autoDistributeButton.Click += AutoDistributeButton_Click;
            operationsGroupBox.Controls.Add(autoDistributeButton);

            // Clear All Button
            clearAllButton = new Button();
            clearAllButton.Text = "تفريغ جميع الحقول";
            clearAllButton.Location = new Point(startX + (buttonWidth + buttonSpacing), buttonY);
            clearAllButton.Size = new Size(buttonWidth, buttonHeight);
            clearAllButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearAllButton.BackColor = Color.FromArgb(255, 193, 7);
            clearAllButton.ForeColor = Color.Black;
            clearAllButton.FlatStyle = FlatStyle.Flat;
            clearAllButton.FlatAppearance.BorderSize = 0;
            clearAllButton.Cursor = Cursors.Hand;
            clearAllButton.Click += ClearAllButton_Click;
            operationsGroupBox.Controls.Add(clearAllButton);

            // Redistribute Button
            redistributeButton = new Button();
            redistributeButton.Text = "إعادة التوزيع الآلي";
            redistributeButton.Location = new Point(startX + (buttonWidth + buttonSpacing), buttonY);
            redistributeButton.Size = new Size(buttonWidth, buttonHeight);
            redistributeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            redistributeButton.BackColor = Color.FromArgb(0, 123, 255);
            redistributeButton.ForeColor = Color.White;
            redistributeButton.FlatStyle = FlatStyle.Flat;
            redistributeButton.FlatAppearance.BorderSize = 0;
            redistributeButton.Cursor = Cursors.Hand;
            redistributeButton.Click += RedistributeButton_Click;
            operationsGroupBox.Controls.Add(redistributeButton);

            // Report Button
            reportButton = new Button();
            reportButton.Text = "تقرير";
            reportButton.Location = new Point(startX, buttonY);
            reportButton.Size = new Size(buttonWidth, buttonHeight);
            reportButton.Font = new Font("Arial", 12, FontStyle.Bold);
            reportButton.BackColor = Color.FromArgb(108, 117, 125);
            reportButton.ForeColor = Color.White;
            reportButton.FlatStyle = FlatStyle.Flat;
            reportButton.FlatAppearance.BorderSize = 0;
            reportButton.Cursor = Cursors.Hand;
            reportButton.Click += ReportButton_Click;
            operationsGroupBox.Controls.Add(reportButton);
        }

        private void CreateAcademicYearSection()
        {
            // Academic Year GroupBox
            academicYearGroupBox = new GroupBox();
            academicYearGroupBox.Text = "بيانات العام الدراسي";
            academicYearGroupBox.Location = new Point(790, 20); // To the right of operations group
            academicYearGroupBox.Size = new Size(380, 80);
            academicYearGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            academicYearGroupBox.RightToLeft = RightToLeft.Yes;
            academicYearGroupBox.ForeColor = Color.DarkBlue;
            academicYearGroupBox.BackColor = Color.FromArgb(248, 249, 250);
            academicYearGroupBox.FlatStyle = FlatStyle.Flat;
            this.Controls.Add(academicYearGroupBox);

            // Academic Year Start Date
            Label startDateLabel = new Label();
            startDateLabel.Text = "بداية العام الدراسي";
            startDateLabel.Location = new Point(280, 25);
            startDateLabel.Size = new Size(90, 20);
            startDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            startDateLabel.ForeColor = Color.FromArgb(73, 80, 87);
            startDateLabel.TextAlign = ContentAlignment.MiddleCenter;
            academicYearGroupBox.Controls.Add(startDateLabel);

            academicYearStartPicker = new DateTimePicker();
            academicYearStartPicker.Location = new Point(190, 45);
            academicYearStartPicker.Size = new Size(180, 25);
            academicYearStartPicker.Font = new Font("Arial", 12, FontStyle.Bold);
            academicYearStartPicker.Format = DateTimePickerFormat.Short;
            academicYearStartPicker.Value = new DateTime(DateTime.Now.Year, 9, 1); // Default to September 1st
            academicYearStartPicker.ValueChanged += AcademicYearStartPicker_ValueChanged; // حفظ التغييرات
            academicYearGroupBox.Controls.Add(academicYearStartPicker);

            // Academic Year End Date
            Label endDateLabel = new Label();
            endDateLabel.Text = "نهاية العام الدراسي";
            endDateLabel.Location = new Point(90, 25);
            endDateLabel.Size = new Size(90, 20);
            endDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            endDateLabel.ForeColor = Color.FromArgb(73, 80, 87);
            endDateLabel.TextAlign = ContentAlignment.MiddleCenter;
            academicYearGroupBox.Controls.Add(endDateLabel);

            academicYearEndPicker = new DateTimePicker();
            academicYearEndPicker.Location = new Point(10, 45);
            academicYearEndPicker.Size = new Size(170, 25);
            academicYearEndPicker.Font = new Font("Arial", 12, FontStyle.Bold);
            academicYearEndPicker.Format = DateTimePickerFormat.Short;
            academicYearEndPicker.Value = new DateTime(DateTime.Now.Year + 1, 6, 30); // Default to June 30th next year
            academicYearEndPicker.ValueChanged += AcademicYearEndPicker_ValueChanged; // حفظ التغييرات
            academicYearGroupBox.Controls.Add(academicYearEndPicker);
        }

        private void CreateSearchSection()
        {
            // Search GroupBox - Single row layout
            searchGroupBox = new GroupBox();
            searchGroupBox.Text = "البحث والتصفية";
            searchGroupBox.Location = new Point(20, 120);
            searchGroupBox.Size = new Size(1150, 80);
            searchGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchGroupBox.RightToLeft = RightToLeft.Yes;
            searchGroupBox.ForeColor = Color.DarkBlue;
            searchGroupBox.BackColor = Color.FromArgb(248, 249, 250);
            searchGroupBox.FlatStyle = FlatStyle.Flat;
            this.Controls.Add(searchGroupBox);

            // Single row layout positioned to the right - من اليمين إلى اليسار
            int rowY = 25;
            int labelHeight = 15;
            int controlHeight = 25;
            int spacing = 110; // Spacing between elements

            // Calculate positioning for right alignment with 100px offset from right
            int elementWidth = 100; // Width of each control
            int totalElements = 8; // 6 controls + 2 buttons
            int totalWidth = (elementWidth * totalElements) + (spacing * (totalElements - 1));

            // Position elements to the right with 100px margin from right edge
            int groupBoxWidth = searchGroupBox.Width - 40; // Account for margins
            int rightOffset = 100; // 100px from right edge
            int rightMostX = groupBoxWidth - rightOffset; // Start from right side with offset

            // Center layout - من اليمين إلى اليسار
            // 1. البحث بالاسم (الأول من اليمين)
            Label nameLabel = new Label();
            nameLabel.Text = "البحث بالاسم";
            nameLabel.Location = new Point(rightMostX + 10, rowY);
            nameLabel.Size = new Size(80, labelHeight);
            nameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            nameLabel.ForeColor = Color.FromArgb(73, 80, 87);
            nameLabel.TextAlign = ContentAlignment.MiddleCenter;
            searchGroupBox.Controls.Add(nameLabel);

            searchNameTextBox = new TextBox();
            searchNameTextBox.Location = new Point(rightMostX, rowY + 18);
            searchNameTextBox.Size = new Size(elementWidth, controlHeight);
            searchNameTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchNameTextBox.BackColor = Color.White;
            searchNameTextBox.BorderStyle = BorderStyle.FixedSingle;
            searchNameTextBox.TextChanged += SearchTextBox_TextChanged;
            searchGroupBox.Controls.Add(searchNameTextBox);

            // 2. مسؤول المتابعة (الثاني من اليمين)
            Label supervisorLabel = new Label();
            supervisorLabel.Text = "مسؤول المتابعة";
            supervisorLabel.Location = new Point(rightMostX - spacing + 10, rowY);
            supervisorLabel.Size = new Size(80, labelHeight);
            supervisorLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorLabel.ForeColor = Color.FromArgb(73, 80, 87);
            supervisorLabel.TextAlign = ContentAlignment.MiddleCenter;
            searchGroupBox.Controls.Add(supervisorLabel);

            supervisorComboBox = new ComboBox();
            supervisorComboBox.Location = new Point(rightMostX - spacing, rowY + 18);
            supervisorComboBox.Size = new Size(elementWidth, controlHeight);
            supervisorComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorComboBox.BackColor = Color.White;
            supervisorComboBox.FlatStyle = FlatStyle.Flat;
            supervisorComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            supervisorComboBox.Items.AddRange(new string[] {
                "الكل", "أحمد محمد", "فاطمة علي", "محمد حسن", "نورا أحمد", "سارة محمود"
            });
            supervisorComboBox.SelectedIndex = 0;
            supervisorComboBox.SelectedIndexChanged += SupervisorComboBox_SelectedIndexChanged;
            searchGroupBox.Controls.Add(supervisorComboBox);

            // 3. تاريخ معين (الثالث من اليمين)
            Label dateLabel = new Label();
            dateLabel.Text = "تاريخ معين";
            dateLabel.Location = new Point(rightMostX - spacing * 2 + 10, rowY);
            dateLabel.Size = new Size(80, labelHeight);
            dateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            dateLabel.ForeColor = Color.FromArgb(73, 80, 87);
            dateLabel.TextAlign = ContentAlignment.MiddleCenter;
            searchGroupBox.Controls.Add(dateLabel);

            searchDatePicker = new DateTimePicker();
            searchDatePicker.Location = new Point(rightMostX - spacing * 2, rowY + 18);
            searchDatePicker.Size = new Size(elementWidth, controlHeight);
            searchDatePicker.Font = new Font("Arial", 12, FontStyle.Bold);
            searchDatePicker.Format = DateTimePickerFormat.Short;
            searchDatePicker.ValueChanged += SearchDatePicker_ValueChanged;
            searchGroupBox.Controls.Add(searchDatePicker);

            // 4. من تاريخ (الرابع من اليمين)
            Label startDateLabel = new Label();
            startDateLabel.Text = "من تاريخ";
            startDateLabel.Location = new Point(rightMostX - spacing * 3 + 10, rowY);
            startDateLabel.Size = new Size(80, labelHeight);
            startDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            startDateLabel.ForeColor = Color.FromArgb(73, 80, 87);
            startDateLabel.TextAlign = ContentAlignment.MiddleCenter;
            searchGroupBox.Controls.Add(startDateLabel);

            startDatePicker = new DateTimePicker();
            startDatePicker.Location = new Point(rightMostX - spacing * 3, rowY + 18);
            startDatePicker.Size = new Size(elementWidth, controlHeight);
            startDatePicker.Font = new Font("Arial", 12, FontStyle.Bold);
            startDatePicker.Format = DateTimePickerFormat.Short;
            startDatePicker.ValueChanged += DateRangePicker_ValueChanged;
            searchGroupBox.Controls.Add(startDatePicker);

            // 5. إلى تاريخ (الخامس من اليمين)
            Label endDateLabel = new Label();
            endDateLabel.Text = "إلى تاريخ";
            endDateLabel.Location = new Point(rightMostX - spacing * 4 + 10, rowY);
            endDateLabel.Size = new Size(80, labelHeight);
            endDateLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            endDateLabel.ForeColor = Color.FromArgb(73, 80, 87);
            endDateLabel.TextAlign = ContentAlignment.MiddleCenter;
            searchGroupBox.Controls.Add(endDateLabel);

            endDatePicker = new DateTimePicker();
            endDatePicker.Location = new Point(rightMostX - spacing * 4, rowY + 18);
            endDatePicker.Size = new Size(elementWidth, controlHeight);
            endDatePicker.Font = new Font("Arial", 12, FontStyle.Bold);
            endDatePicker.Format = DateTimePickerFormat.Short;
            endDatePicker.ValueChanged += DateRangePicker_ValueChanged;
            searchGroupBox.Controls.Add(endDatePicker);

            // 6. مستوى التنفيذ (السادس من اليمين)
            Label executionLevelLabel = new Label();
            executionLevelLabel.Text = "مستوى التنفيذ";
            executionLevelLabel.Location = new Point(rightMostX - spacing * 5 + 10, rowY);
            executionLevelLabel.Size = new Size(80, labelHeight);
            executionLevelLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            executionLevelLabel.ForeColor = Color.FromArgb(73, 80, 87);
            executionLevelLabel.TextAlign = ContentAlignment.MiddleCenter;
            searchGroupBox.Controls.Add(executionLevelLabel);

            executionLevelSearchComboBox = new ComboBox();
            executionLevelSearchComboBox.Location = new Point(rightMostX - spacing * 5, rowY + 18);
            executionLevelSearchComboBox.Size = new Size(elementWidth, controlHeight);
            executionLevelSearchComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            executionLevelSearchComboBox.BackColor = Color.White;
            executionLevelSearchComboBox.FlatStyle = FlatStyle.Flat;
            executionLevelSearchComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            executionLevelSearchComboBox.Items.AddRange(new string[] {
                "الكل", "نعم", "لا", "غير محدد"
            });
            executionLevelSearchComboBox.SelectedIndex = 0;
            executionLevelSearchComboBox.SelectedIndexChanged += ExecutionLevelSearchComboBox_SelectedIndexChanged;
            searchGroupBox.Controls.Add(executionLevelSearchComboBox);

            // 7. زر بحث (السابع من اليمين)
            searchButton = new Button();
            searchButton.Text = "بحث";
            searchButton.Location = new Point(rightMostX - spacing * 6 + 10, rowY + 15);
            searchButton.Size = new Size(elementWidth - 20, 30);
            searchButton.Font = new Font("Arial", 12, FontStyle.Bold);
            searchButton.BackColor = Color.FromArgb(0, 123, 255);
            searchButton.ForeColor = Color.White;
            searchButton.FlatStyle = FlatStyle.Flat;
            searchButton.FlatAppearance.BorderSize = 0;
            searchButton.Cursor = Cursors.Hand;
            searchButton.Click += SearchButton_Click;
            searchGroupBox.Controls.Add(searchButton);

            // 8. زر مسح البحث (الثامن من اليمين)
            clearSearchButton = new Button();
            clearSearchButton.Text = "مسح البحث";
            clearSearchButton.Location = new Point(rightMostX - spacing * 7 + 10, rowY + 15);
            clearSearchButton.Size = new Size(elementWidth - 10, 30);
            clearSearchButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearSearchButton.BackColor = Color.FromArgb(108, 117, 125);
            clearSearchButton.ForeColor = Color.White;
            clearSearchButton.FlatStyle = FlatStyle.Flat;
            clearSearchButton.FlatAppearance.BorderSize = 0;
            clearSearchButton.Cursor = Cursors.Hand;
            clearSearchButton.Click += ClearSearchButton_Click;
            searchGroupBox.Controls.Add(clearSearchButton);
        }

        private void CreateDataGridView()
        {
            // CSS-like table styling with editing enabled
            distributionDataGridView = new DataGridView();
            distributionDataGridView.Location = new Point(20, 220); // Adjusted for smaller search section
            distributionDataGridView.Size = new Size(1150, 390); // Increased height
            distributionDataGridView.Font = new Font("Arial", 12, FontStyle.Bold);
            distributionDataGridView.AllowUserToAddRows = false;
            distributionDataGridView.AllowUserToDeleteRows = false;
            distributionDataGridView.ReadOnly = false; // Enable editing
            distributionDataGridView.SelectionMode = DataGridViewSelectionMode.CellSelect; // Cell selection for editing
            distributionDataGridView.MultiSelect = false;
            distributionDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None; // Manual column sizing
            distributionDataGridView.RowHeadersVisible = false;
            distributionDataGridView.BackgroundColor = Color.White;
            distributionDataGridView.BorderStyle = BorderStyle.Fixed3D;
            distributionDataGridView.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            distributionDataGridView.GridColor = Color.FromArgb(222, 226, 230);
            distributionDataGridView.DefaultCellStyle.BackColor = Color.White;
            distributionDataGridView.DefaultCellStyle.ForeColor = Color.FromArgb(73, 80, 87);
            distributionDataGridView.DefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            distributionDataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            distributionDataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            distributionDataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            distributionDataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.FromArgb(73, 80, 87);
            distributionDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            distributionDataGridView.EnableHeadersVisualStyles = false;

            // Add event handlers for editing
            distributionDataGridView.CellEndEdit += DistributionDataGridView_CellEndEdit;
            distributionDataGridView.CellValidating += DistributionDataGridView_CellValidating;
            distributionDataGridView.EditingControlShowing += DistributionDataGridView_EditingControlShowing;

            this.Controls.Add(distributionDataGridView);

            // Add columns
            distributionDataGridView.Columns.Add("TeacherName", "اسم المعلم");
            distributionDataGridView.Columns.Add("Preparation1", "تحضير1");
            distributionDataGridView.Columns.Add("Homework1", "واجبات1");
            distributionDataGridView.Columns.Add("Visit1", "زيارة1");
            distributionDataGridView.Columns.Add("Preparation2", "تحضير2");
            distributionDataGridView.Columns.Add("Homework2", "واجبات2");
            distributionDataGridView.Columns.Add("Visit2", "زيارة2");
            // Add ComboBox column for Supervisor
            DataGridViewComboBoxColumn supervisorColumn = new DataGridViewComboBoxColumn();
            supervisorColumn.Name = "Supervisor";
            supervisorColumn.HeaderText = "مسؤول المتابعة";
            supervisorColumn.Items.AddRange(new string[] { "", "أحمد محمد", "فاطمة علي", "محمد حسن", "نورا أحمد", "سارة محمود" });
            supervisorColumn.Width = 150;
            distributionDataGridView.Columns.Add(supervisorColumn);

            // Add ComboBox column for execution level
            DataGridViewComboBoxColumn executionLevelColumn = new DataGridViewComboBoxColumn();
            executionLevelColumn.Name = "ExecutionLevel";
            executionLevelColumn.HeaderText = "مستوى التنفيذ";
            executionLevelColumn.Items.AddRange(new string[] { "", "نعم", "لا" });
            executionLevelColumn.Width = 120;
            distributionDataGridView.Columns.Add(executionLevelColumn);

            // Set column properties with optimized widths to fill the table
            distributionDataGridView.Columns["TeacherName"].ReadOnly = true; // Teacher name shouldn't be editable
            distributionDataGridView.Columns["TeacherName"].Width = 200; // Increased for better name display
            distributionDataGridView.Columns["Preparation1"].Width = 110;
            distributionDataGridView.Columns["Homework1"].Width = 110;
            distributionDataGridView.Columns["Visit1"].Width = 110;
            distributionDataGridView.Columns["Preparation2"].Width = 110;
            distributionDataGridView.Columns["Homework2"].Width = 110;
            distributionDataGridView.Columns["Visit2"].Width = 110;
            distributionDataGridView.Columns["Supervisor"].Width = 190; // Increased to fill remaining space
            distributionDataGridView.Columns["ExecutionLevel"].Width = 100;
        }

        private void CreateRecordCountLabel()
        {
            recordCountLabel = new Label();
            recordCountLabel.Location = new Point(20, 630); // Adjusted for new layout
            recordCountLabel.Size = new Size(300, 25);
            recordCountLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            recordCountLabel.ForeColor = Color.FromArgb(73, 80, 87);
            recordCountLabel.Text = "إجمالي السجلات: 0";
            this.Controls.Add(recordCountLabel);
        }

        private void LoadTeachersFromFile()
        {
            try
            {
                // مسار ملف بيانات المعلمين
                string teachersFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Teachers.txt");

                if (File.Exists(teachersFilePath))
                {
                    string[] lines = File.ReadAllLines(teachersFilePath);
                    foreach (string line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            string[] parts = line.Split('|');
                            if (parts.Length >= 8) // جميع البيانات مع مسؤول المتابعة
                            {
                                string teacherName = parts[1]; // الاسم في العمود الثاني
                                string supervisor = parts[7]; // مسؤول المتابعة في العمود الثامن

                                // إضافة المعلم إلى قائمة التوزيع بدون تواريخ
                                distributionRecords.Add(new DistributionRecord
                                {
                                    TeacherName = teacherName,
                                    Preparation1 = "",
                                    Homework1 = "",
                                    Visit1 = "",
                                    Preparation2 = "",
                                    Homework2 = "",
                                    Visit2 = "",
                                    Supervisor = supervisor, // أخذ مسؤول المتابعة من ملف المعلمين
                                    ExecutionLevel = ""
                                });
                            }
                        }
                    }
                }

                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل بيانات المعلمين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            distributionDataGridView.Rows.Clear();
            foreach (var record in distributionRecords)
            {
                distributionDataGridView.Rows.Add(
                    record.TeacherName,
                    record.Preparation1,
                    record.Homework1,
                    record.Visit1,
                    record.Preparation2,
                    record.Homework2,
                    record.Visit2,
                    record.Supervisor,
                    record.ExecutionLevel
                );
            }
            UpdateRecordCount();
            // تلوين التواريخ غير متاح حالياً
        }

        private void UpdateRecordCount()
        {
            recordCountLabel.Text = $"إجمالي السجلات: {distributionDataGridView.Rows.Count}";
        }

        // Event Handlers
        private void AutoDistributeButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من التواريخ غير متاح حالياً

                // رسالة تأكيد قبل التوزيع
                var confirmMessage = $"هل أنت متأكد من التوزيع الآلي؟\n\n" +
                                   $"بداية العام الدراسي: {academicYearStartPicker.Value:yyyy/MM/dd}\n" +
                                   $"نهاية العام الدراسي: {academicYearEndPicker.Value:yyyy/MM/dd}\n\n" +
                                   $"سيبدأ التوزيع بعد 20 يوم من بداية العام\n" +
                                   $"وينتهي قبل 20 يوم من نهاية العام";

                var result = MessageBox.Show(confirmMessage, "تأكيد التوزيع الآلي",
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    MessageBox.Show("وظيفة التوزيع الآلي غير متاحة حالياً.", "غير متاح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التوزيع الآلي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearAllButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من تفريغ جميع الحقول؟", "تأكيد التفريغ",
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    foreach (var record in distributionRecords)
                    {
                        record.Preparation1 = "";
                        record.Homework1 = "";
                        record.Visit1 = "";
                        record.Preparation2 = "";
                        record.Homework2 = "";
                        record.Visit2 = "";
                        record.Supervisor = "";
                        record.ExecutionLevel = "";
                    }

                    RefreshDataGridView();
                    MessageBox.Show("تم تفريغ جميع الحقول بنجاح!", "تفريغ الحقول", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التفريغ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RedistributeButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من إعادة التوزيع الآلي؟\nسيتم استبدال التوزيع الحالي.",
                                           "تأكيد إعادة التوزيع", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    AutoDistributeButton_Click(sender, e);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إعادة التوزيع: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SearchDatePicker_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DateRangePicker_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SupervisorComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ClearSearchButton_Click(object sender, EventArgs e)
        {
            searchNameTextBox.Text = "";
            searchDatePicker.Value = DateTime.Now;
            startDatePicker.Value = DateTime.Now.AddDays(-30);
            endDatePicker.Value = DateTime.Now;
            supervisorComboBox.SelectedIndex = 0;
            executionLevelSearchComboBox.SelectedIndex = 0;
            ApplyFilters();
        }

        private void ExecutionLevelSearchComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ReportButton_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء محتوى التقرير
                var reportContent = GenerateDistributionReport();

                // إنشاء نافذة التقرير
                var reportForm = new ReportForm(reportContent);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var filteredRecords = distributionRecords.AsEnumerable();

                // Filter by name
                if (!string.IsNullOrEmpty(searchNameTextBox.Text))
                {
                    filteredRecords = filteredRecords.Where(r => r.TeacherName.Contains(searchNameTextBox.Text));
                }

                // Filter by supervisor
                if (supervisorComboBox.SelectedIndex > 0)
                {
                    filteredRecords = filteredRecords.Where(r => r.Supervisor == supervisorComboBox.SelectedItem.ToString());
                }

                // Filter by execution level
                if (executionLevelSearchComboBox.SelectedIndex > 0)
                {
                    var selectedLevel = executionLevelSearchComboBox.SelectedItem.ToString();
                    if (selectedLevel == "غير محدد")
                    {
                        filteredRecords = filteredRecords.Where(r => string.IsNullOrEmpty(r.ExecutionLevel));
                    }
                    else
                    {
                        filteredRecords = filteredRecords.Where(r => r.ExecutionLevel == selectedLevel);
                    }
                }

                // Apply filtered results to DataGridView
                distributionDataGridView.Rows.Clear();
                foreach (var record in filteredRecords)
                {
                    distributionDataGridView.Rows.Add(
                        record.TeacherName,
                        record.Preparation1,
                        record.Homework1,
                        record.Visit1,
                        record.Preparation2,
                        record.Homework2,
                        record.Visit2,
                        record.Supervisor,
                        record.ExecutionLevel
                    );
                }
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التصفية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Event handlers for editing functionality
        private void DistributionDataGridView_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.RowIndex < distributionRecords.Count)
                {
                    var record = distributionRecords[e.RowIndex];
                    var columnName = distributionDataGridView.Columns[e.ColumnIndex].Name;
                    var newValue = distributionDataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value?.ToString() ?? "";

                    // Update the corresponding property in the record
                    switch (columnName)
                    {
                        case "Preparation1":
                            record.Preparation1 = newValue;
                            break;
                        case "Homework1":
                            record.Homework1 = newValue;
                            break;
                        case "Visit1":
                            record.Visit1 = newValue;
                            break;
                        case "Preparation2":
                            record.Preparation2 = newValue;
                            break;
                        case "Homework2":
                            record.Homework2 = newValue;
                            break;
                        case "Visit2":
                            record.Visit2 = newValue;
                            break;
                        case "Supervisor":
                            record.Supervisor = newValue;
                            break;
                        case "ExecutionLevel":
                            record.ExecutionLevel = newValue;
                            break;
                    }

                    // Auto-save indication (you could add a visual indicator here)
                    distributionDataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Style.BackColor = Color.LightGreen;

                    // Reset color after a short delay
                    System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
                    timer.Interval = 1000; // 1 second
                    timer.Tick += (s, args) =>
                    {
                        distributionDataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Style.BackColor = Color.White;
                        timer.Stop();
                        timer.Dispose();
                    };
                    timer.Start();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الحفظ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DistributionDataGridView_CellValidating(object sender, DataGridViewCellValidatingEventArgs e)
        {
            try
            {
                var columnName = distributionDataGridView.Columns[e.ColumnIndex].Name;
                var value = e.FormattedValue.ToString();

                // Validate date fields
                if (columnName.Contains("Preparation") || columnName.Contains("Homework") || columnName.Contains("Visit"))
                {
                    if (!string.IsNullOrEmpty(value))
                    {
                        if (!DateTime.TryParse(value, out _))
                        {
                            MessageBox.Show("يرجى إدخال تاريخ صحيح بالصيغة: yyyy/MM/dd", "تاريخ غير صحيح",
                                          MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            e.Cancel = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التحقق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DistributionDataGridView_EditingControlShowing(object sender, DataGridViewEditingControlShowingEventArgs e)
        {
            try
            {
                // Handle ComboBox selection for ExecutionLevel to ensure only one option is selected
                if (distributionDataGridView.CurrentCell.OwningColumn.Name == "ExecutionLevel")
                {
                    ComboBox comboBox = e.Control as ComboBox;
                    if (comboBox != null)
                    {
                        comboBox.DropDownStyle = ComboBoxStyle.DropDownList;
                    }
                }
                else if (distributionDataGridView.CurrentCell.OwningColumn.Name == "Supervisor")
                {
                    ComboBox comboBox = e.Control as ComboBox;
                    if (comboBox != null)
                    {
                        comboBox.DropDownStyle = ComboBoxStyle.DropDownList;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في التحكم بالتعديل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateDistributionReport()
        {
            var reportBuilder = new System.Text.StringBuilder();

            // CSS-like header with modern styling
            reportBuilder.AppendLine("╔" + "═".PadLeft(118, '═') + "╗");
            reportBuilder.AppendLine("║" + "تقرير التوزيع الآلي للمهام".PadLeft(70).PadRight(118) + "║");
            reportBuilder.AppendLine("║" + $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}".PadLeft(75).PadRight(118) + "║");
            reportBuilder.AppendLine("╚" + "═".PadLeft(118, '═') + "╝");
            reportBuilder.AppendLine();

            // الحصول على البيانات المعروضة حالياً (المفلترة)
            var displayedRecords = new List<DistributionRecord>();
            foreach (DataGridViewRow row in distributionDataGridView.Rows)
            {
                if (row.Cells[0].Value != null)
                {
                    displayedRecords.Add(new DistributionRecord
                    {
                        TeacherName = row.Cells[0].Value?.ToString() ?? "",
                        Preparation1 = row.Cells[1].Value?.ToString() ?? "",
                        Homework1 = row.Cells[2].Value?.ToString() ?? "",
                        Visit1 = row.Cells[3].Value?.ToString() ?? "",
                        Preparation2 = row.Cells[4].Value?.ToString() ?? "",
                        Homework2 = row.Cells[5].Value?.ToString() ?? "",
                        Visit2 = row.Cells[6].Value?.ToString() ?? "",
                        Supervisor = row.Cells[7].Value?.ToString() ?? "",
                        ExecutionLevel = row.Cells[8].Value?.ToString() ?? ""
                    });
                }
            }

            // CSS-like table header with borders
            reportBuilder.AppendLine("┌─────┬──────────────────────┬──────────────┬──────────────┬──────────────┬──────────────┬──────────────┬──────────────┬────────────────────┬──────────────┐");
            reportBuilder.AppendLine($"│ {"م",-3} │ {"الاسم",-20} │ {"تحضير1",-12} │ {"واجبات1",-12} │ {"زيارة1",-12} │ {"تحضير2",-12} │ {"واجبات2",-12} │ {"زيارة2",-12} │ {"مسؤول المتابعة",-18} │ {"مستوى التنفيذ",-12} │");
            reportBuilder.AppendLine("├─────┼──────────────────────┼──────────────┼──────────────┼──────────────┼──────────────┼──────────────┼──────────────┼────────────────────┼──────────────┤");

            // بيانات المعلمين مع حدود CSS-like
            int counter = 1;
            foreach (var record in displayedRecords)
            {
                var executionLevel = string.IsNullOrEmpty(record.ExecutionLevel) ? "غير محدد" : record.ExecutionLevel;
                var preparation1 = string.IsNullOrEmpty(record.Preparation1) ? "-" : record.Preparation1;
                var homework1 = string.IsNullOrEmpty(record.Homework1) ? "-" : record.Homework1;
                var visit1 = string.IsNullOrEmpty(record.Visit1) ? "-" : record.Visit1;
                var preparation2 = string.IsNullOrEmpty(record.Preparation2) ? "-" : record.Preparation2;
                var homework2 = string.IsNullOrEmpty(record.Homework2) ? "-" : record.Homework2;
                var visit2 = string.IsNullOrEmpty(record.Visit2) ? "-" : record.Visit2;
                var supervisor = string.IsNullOrEmpty(record.Supervisor) ? "-" : record.Supervisor;

                reportBuilder.AppendLine($"│ {counter,-3} │ {record.TeacherName,-20} │ {preparation1,-12} │ {homework1,-12} │ {visit1,-12} │ {preparation2,-12} │ {homework2,-12} │ {visit2,-12} │ {supervisor,-18} │ {executionLevel,-12} │");

                // Add separator line between rows (except for last row)
                if (counter < displayedRecords.Count)
                {
                    reportBuilder.AppendLine("├─────┼──────────────────────┼──────────────┼──────────────┼──────────────┼──────────────┼──────────────┼──────────────┼────────────────────┼──────────────┤");
                }
                counter++;
            }

            // CSS-like table footer
            reportBuilder.AppendLine("└─────┴──────────────────────┴──────────────┴──────────────┴──────────────┴──────────────┴──────────────┴──────────────┴────────────────────┴──────────────┘");

            reportBuilder.AppendLine();

            // CSS-like summary box
            reportBuilder.AppendLine("┌" + "─".PadLeft(50, '─') + "┐");
            reportBuilder.AppendLine($"│ {"إجمالي السجلات المعروضة: " + displayedRecords.Count,-48} │");
            reportBuilder.AppendLine($"│ {"تاريخ الإنشاء: " + DateTime.Now:yyyy/MM/dd HH:mm:ss,-48} │");
            reportBuilder.AppendLine("└" + "─".PadLeft(50, '─') + "┘");

            return reportBuilder.ToString();
        }













        public void AddNewTeacherWithAutoDistribution(Teacher teacher)
        {
            try
            {
                // التحقق من وجود المعلم مسبقاً
                var existingRecord = distributionRecords.FirstOrDefault(r => r.TeacherName == teacher.Name);
                if (existingRecord != null)
                {
                    MessageBox.Show($"المعلم '{teacher.Name}' موجود مسبقاً في قائمة التوزيع.",
                                  "معلم موجود", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // التحقق من التواريخ غير متاح حالياً

                // رسالة تأكيد قبل إضافة المعلم مع التوزيع
                var confirmMessage = $"هل تريد إضافة المعلم '{teacher.Name}' مع التوزيع التلقائي؟\n\n" +
                                   $"بداية العام الدراسي: {academicYearStartPicker.Value:yyyy/MM/dd}\n" +
                                   $"نهاية العام الدراسي: {academicYearEndPicker.Value:yyyy/MM/dd}\n\n" +
                                   $"سيبدأ التوزيع بعد 20 يوم من بداية العام\n" +
                                   $"وينتهي قبل 20 يوم من نهاية العام\n\n" +
                                   $"ملاحظة: لن يتم تحديث تواريخ المعلمين الآخرين";

                var result = MessageBox.Show(confirmMessage, "تأكيد إضافة المعلم مع التوزيع",
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // إنشاء سجل توزيع جديد للمعلم
                    var newRecord = new DistributionRecord
                    {
                        TeacherName = teacher.Name,
                        Preparation1 = "",
                        Homework1 = "",
                        Visit1 = "",
                        Preparation2 = "",
                        Homework2 = "",
                        Visit2 = "",
                        Supervisor = teacher.Supervisor, // أخذ مسؤول المتابعة من بيانات المعلم
                        ExecutionLevel = ""
                    };

                    // إضافة السجل إلى القائمة
                    distributionRecords.Add(newRecord);

                    // التوزيع التلقائي غير متاح حالياً

                    // تحديث الجدول مع التلوين
                    RefreshDataGridView();

                    MessageBox.Show($"تم إضافة المعلم '{teacher.Name}' مع التوزيع التلقائي بنجاح!",
                                  "تم الإضافة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المعلم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void UpdateTeacherData(Teacher teacher)
        {
            try
            {
                // البحث عن سجل المعلم في قائمة التوزيع
                var record = distributionRecords.FirstOrDefault(r => r.TeacherName == teacher.Name);
                if (record != null)
                {
                    // تحديث اسم المعلم فقط (الاحتفاظ بالتواريخ الموجودة)
                    record.TeacherName = teacher.Name;
                    RefreshDataGridView();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث بيانات المعلم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private void LoadAcademicYearSettings()
        {
            try
            {
                if (File.Exists(settingsFilePath))
                {
                    string[] lines = File.ReadAllLines(settingsFilePath);
                    if (lines.Length >= 2)
                    {
                        if (DateTime.TryParse(lines[0], out DateTime startDate))
                        {
                            academicYearStartPicker.Value = startDate;
                        }

                        if (DateTime.TryParse(lines[1], out DateTime endDate))
                        {
                            academicYearEndPicker.Value = endDate;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، استخدم القيم الافتراضية
                MessageBox.Show($"تعذر تحميل إعدادات العام الدراسي: {ex.Message}\nسيتم استخدام القيم الافتراضية.",
                              "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void SaveAcademicYearSettings()
        {
            try
            {
                // إنشاء المجلد إذا لم يكن موجوداً
                string directory = Path.GetDirectoryName(settingsFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // حفظ التواريخ في الملف
                string[] lines = new string[]
                {
                    academicYearStartPicker.Value.ToString("yyyy-MM-dd"),
                    academicYearEndPicker.Value.ToString("yyyy-MM-dd")
                };

                File.WriteAllLines(settingsFilePath, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ إعدادات العام الدراسي: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AcademicYearStartPicker_ValueChanged(object sender, EventArgs e)
        {
            SaveAcademicYearSettings();
        }

        private void AcademicYearEndPicker_ValueChanged(object sender, EventArgs e)
        {
            SaveAcademicYearSettings();
        }
    }

    // Helper class for distribution records
    public class DistributionRecord
    {
        public string TeacherName { get; set; } = "";
        public string Preparation1 { get; set; } = "";
        public string Homework1 { get; set; } = "";
        public string Visit1 { get; set; } = "";
        public string Preparation2 { get; set; } = "";
        public string Homework2 { get; set; } = "";
        public string Visit2 { get; set; } = "";
        public string Supervisor { get; set; } = "";
        public string ExecutionLevel { get; set; } = "";
    }
}
