using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class ReportForm : Form
    {
        private string reportContent;
        private TextBox reportTextBox;
        private Button printButton;
        private Button saveButton;
        private Button closeButton;

        public ReportForm(string content)
        {
            reportContent = content;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "تقرير بيانات المعلمين";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 500);

            // Report TextBox
            reportTextBox = new TextBox();
            reportTextBox.Location = new Point(20, 20);
            reportTextBox.Size = new Size(840, 480);
            reportTextBox.Font = new Font("Courier New", 10, FontStyle.Bold);
            reportTextBox.Multiline = true;
            reportTextBox.ScrollBars = ScrollBars.Both;
            reportTextBox.ReadOnly = true;
            reportTextBox.Text = reportContent;
            reportTextBox.BackColor = Color.White;
            reportTextBox.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.Controls.Add(reportTextBox);

            // Buttons Panel
            Panel buttonsPanel = new Panel();
            buttonsPanel.Location = new Point(20, 520);
            buttonsPanel.Size = new Size(840, 50);
            buttonsPanel.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.Controls.Add(buttonsPanel);

            // Print Button
            printButton = new Button();
            printButton.Text = "طباعة";
            printButton.Location = new Point(650, 10);
            printButton.Size = new Size(100, 35);
            printButton.Font = new Font("Arial", 12, FontStyle.Bold);
            printButton.BackColor = Color.LightGreen;
            printButton.FlatStyle = FlatStyle.Flat;
            printButton.Click += PrintButton_Click;
            buttonsPanel.Controls.Add(printButton);

            // Save Button
            saveButton = new Button();
            saveButton.Text = "حفظ كملف";
            saveButton.Location = new Point(530, 10);
            saveButton.Size = new Size(100, 35);
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.BackColor = Color.LightBlue;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.Click += SaveButton_Click;
            buttonsPanel.Controls.Add(saveButton);

            // Close Button
            closeButton = new Button();
            closeButton.Text = "إغلاق";
            closeButton.Location = new Point(410, 10);
            closeButton.Size = new Size(100, 35);
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.BackColor = Color.LightCoral;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.Click += CloseButton_Click;
            buttonsPanel.Controls.Add(closeButton);

            this.ResumeLayout(false);
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            try
            {
                PrintDialog printDialog = new PrintDialog();
                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                
                printDocument.PrintPage += (s, ev) =>
                {
                    ev.Graphics.DrawString(reportContent, new Font("Courier New", 10), 
                                         Brushes.Black, ev.MarginBounds, StringFormat.GenericTypographic);
                };

                printDialog.Document = printDocument;
                
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    printDocument.Print();
                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح!", "طباعة", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ في الطباعة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "ملفات نصية (*.txt)|*.txt|جميع الملفات (*.*)|*.*";
                saveFileDialog.DefaultExt = "txt";
                saveFileDialog.FileName = $"تقرير_المعلمين_{DateTime.Now:yyyyMMdd_HHmm}.txt";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    File.WriteAllText(saveFileDialog.FileName, reportContent, System.Text.Encoding.UTF8);
                    MessageBox.Show($"تم حفظ التقرير بنجاح في:\n{saveFileDialog.FileName}", "حفظ التقرير", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ التقرير: {ex.Message}", "خطأ في الحفظ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
