using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class AddWorkForm : Form
    {
        // مسار ملف حفظ الأعمال الحالية
        private readonly string worksFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Works.txt");

        // Controls
        private TextBox workTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;
        private DataGridView worksDataGridView;
        private List<string> works;

        public AddWorkForm()
        {
            works = new List<string>();
            InitializeComponent();
            LoadWorks();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إضافة عمل";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "إضافة عمل";
            titleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(500, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Left side - Input section
            CreateInputSection();

            // Right side - DataGridView
            CreateDataGridView();

            this.ResumeLayout(false);
        }

        private void CreateInputSection()
        {
            // Work Label
            Label workLabel = new Label();
            workLabel.Text = "العمل الحالي";
            workLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            workLabel.Size = new Size(100, 25);
            workLabel.Location = new Point(30, 70);
            this.Controls.Add(workLabel);

            // Work TextBox
            workTextBox = new TextBox();
            workTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            workTextBox.Size = new Size(200, 30);
            workTextBox.Location = new Point(30, 100);
            this.Controls.Add(workTextBox);

            // Buttons - vertical layout
            CreateButtons();
        }

        private void CreateButtons()
        {
            // Save Button
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(200, 35);
            saveButton.Location = new Point(30, 130);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // Edit Button
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(200, 35);
            editButton.Location = new Point(30, 175);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Delete Button
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(200, 35);
            deleteButton.Location = new Point(30, 220);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // Close Button
            closeButton = new Button();
            closeButton.Text = "خروج من النافذة";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(200, 35);
            closeButton.Location = new Point(30, 265);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);
        }

        private void CreateDataGridView()
        {
            // DataGridView for works
            worksDataGridView = new DataGridView();
            worksDataGridView.Location = new Point(260, 70);
            worksDataGridView.Size = new Size(200, 230);
            worksDataGridView.BackgroundColor = Color.White;
            worksDataGridView.BorderStyle = BorderStyle.FixedSingle;
            worksDataGridView.AllowUserToAddRows = false;
            worksDataGridView.AllowUserToDeleteRows = false;
            worksDataGridView.ReadOnly = true;
            worksDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            worksDataGridView.MultiSelect = false;
            worksDataGridView.Font = new Font("Arial", 12, FontStyle.Bold);
            worksDataGridView.RowHeadersVisible = false;
            worksDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            // زيادة ارتفاع عنوان الجدول
            worksDataGridView.ColumnHeadersHeight = 40;
            worksDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

            // Add column
            DataGridViewTextBoxColumn workColumn = new DataGridViewTextBoxColumn();
            workColumn.Name = "Work";
            workColumn.HeaderText = "العمل الحالي";
            workColumn.DataPropertyName = "Work";
            workColumn.DefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);
            worksDataGridView.Columns.Add(workColumn);

            // Set header font
            worksDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 12, FontStyle.Bold);

            // Event handlers
            worksDataGridView.SelectionChanged += WorksDataGridView_SelectionChanged;

            this.Controls.Add(worksDataGridView);
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                string newWork = workTextBox.Text.Trim();
                
                if (works.Contains(newWork))
                {
                    MessageBox.Show("هذا العمل موجود مسبقاً!", "عمل مكرر", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                works.Add(newWork);
                SaveWorks();
                RefreshDataGridView();
                workTextBox.Clear();
                
                MessageBox.Show("تم حفظ العمل بنجاح!", "تأكيد الحفظ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (worksDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عمل للتعديل", "لم يتم اختيار عمل", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            int selectedIndex = worksDataGridView.SelectedRows[0].Index;
            string newWork = workTextBox.Text.Trim();
            
            if (works.Contains(newWork) && works[selectedIndex] != newWork)
            {
                MessageBox.Show("هذا العمل موجود مسبقاً!", "عمل مكرر", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            works[selectedIndex] = newWork;
            SaveWorks();
            RefreshDataGridView();
            workTextBox.Clear();
            
            MessageBox.Show("تم تعديل العمل بنجاح!", "تأكيد التعديل", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (worksDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عمل للحذف", "لم يتم اختيار عمل", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedIndex = worksDataGridView.SelectedRows[0].Index;
            string selectedWork = works[selectedIndex];

            var result = MessageBox.Show($"هل أنت متأكد من حذف العمل '{selectedWork}'؟", 
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            
            if (result == DialogResult.Yes)
            {
                works.RemoveAt(selectedIndex);
                SaveWorks();
                RefreshDataGridView();
                workTextBox.Clear();
                
                MessageBox.Show("تم حذف العمل بنجاح!", "تأكيد الحذف", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void WorksDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (worksDataGridView.SelectedRows.Count > 0)
            {
                int selectedIndex = worksDataGridView.SelectedRows[0].Index;
                if (selectedIndex < works.Count)
                {
                    workTextBox.Text = works[selectedIndex];
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(workTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العمل", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                workTextBox.Focus();
                return false;
            }
            return true;
        }

        private void SaveWorks()
        {
            try
            {
                string directory = Path.GetDirectoryName(worksFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllLines(worksFilePath, works);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ الأعمال: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadWorks()
        {
            try
            {
                if (File.Exists(worksFilePath))
                {
                    works = File.ReadAllLines(worksFilePath).Where(s => !string.IsNullOrWhiteSpace(s)).ToList();
                }
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل الأعمال: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            worksDataGridView.DataSource = null;
            worksDataGridView.DataSource = works.Select(w => new { Work = w }).ToList();
        }
    }
}
