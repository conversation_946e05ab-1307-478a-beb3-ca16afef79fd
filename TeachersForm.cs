using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class TeachersForm : Form
    {
        private List<Teacher> teachers;
        private List<Teacher> filteredTeachers;
        private int nextId = 1;
        private int selectedTeacherId = -1;
        private AutoDistributionForm autoDistributionForm;

        // مسار ملف حفظ بيانات المعلمين
        private readonly string teachersFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "Teachers.txt");

        // Controls
        private TextBox nameTextBox;
        private TextBox registrationNumberTextBox;
        private ComboBox subjectComboBox;
        private ComboBox stageComboBox;
        private ComboBox classHoursComboBox;
        private TextBox phoneTextBox;
        private ComboBox supervisorComboBox;
        private GroupBox inputGroupBox;
        private GroupBox buttonsGroupBox;
        private DataGridView teachersDataGridView;
        private TextBox searchNameTextBox;
        private ComboBox searchSubjectComboBox;
        private ComboBox searchStageComboBox;
        private Label teacherCountLabel;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button clearButton;
        private Button reportButton;
        private Button clearSearchButton;

        public TeachersForm()
        {
            teachers = new List<Teacher>();
            filteredTeachers = new List<Teacher>();
            InitializeComponent();
            LoadTeachersFromFile(); // تحميل المعلمين من الملف
            RefreshDataGrid();
            UpdateTeacherCount();
        }

        public TeachersForm(AutoDistributionForm distributionForm) : this()
        {
            autoDistributionForm = distributionForm;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "إدارة بيانات المعلمين";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;

            CreateInputControls();
            CreateDataGridView();
            CreateSearchControls();
            CreateButtons();
            CreateCountLabel();

            this.ResumeLayout(false);
        }

        private void CreateInputControls()
        {
            // Input GroupBox - CSS-like card design
            inputGroupBox = new GroupBox();
            inputGroupBox.Text = "بيانات المعلم";
            inputGroupBox.Location = new Point(20, 20);
            inputGroupBox.Size = new Size(1100, 160); // توسيع لاستيعاب الصفين
            inputGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            inputGroupBox.RightToLeft = RightToLeft.Yes;
            inputGroupBox.ForeColor = Color.DarkBlue;
            inputGroupBox.BackColor = Color.FromArgb(248, 249, 250); // Light background
            inputGroupBox.FlatStyle = FlatStyle.Flat;
            this.Controls.Add(inputGroupBox);

            int spacing = 180; // المسافة بين الحقول
            int labelY1 = 25; // العناوين في الصف الأول
            int controlY1 = 50; // الحقول في الصف الأول
            int labelY2 = 95; // العناوين في الصف الثاني
            int controlY2 = 120; // الحقول في الصف الثاني
            int startX = 150; // البدء من اليمين (تحريك 100 بكسل لليمين)
            int rightToLeft = 3; // عدد الحقول في الصف الأول - 1 للحساب من اليمين

            // الصف الأول من اليمين إلى اليسار: المرحلة، التخصص، رقم السجل، الاسم

            // Stage Label and ComboBox (الأول من اليمين)
            Label stageLabel = new Label();
            stageLabel.Text = "المرحلة";
            stageLabel.Location = new Point(startX + spacing * rightToLeft + 50, labelY1);
            stageLabel.Size = new Size(70, 20);
            stageLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            stageLabel.ForeColor = Color.FromArgb(73, 80, 87);
            stageLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(stageLabel);

            stageComboBox = new ComboBox();
            stageComboBox.Location = new Point(startX + spacing * rightToLeft, controlY1);
            stageComboBox.Size = new Size(170, 25);
            stageComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            stageComboBox.BackColor = Color.White;
            stageComboBox.ForeColor = Color.FromArgb(73, 80, 87);
            stageComboBox.FlatStyle = FlatStyle.Flat;
            stageComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            stageComboBox.Items.AddRange(new string[] {
                "ابتدائي", "متوسط", "ثانوي", "تربية خاصة"
            });
            inputGroupBox.Controls.Add(stageComboBox);

            // Subject Label and ComboBox (الثاني من اليمين)
            Label subjectLabel = new Label();
            subjectLabel.Text = "التخصص";
            subjectLabel.Location = new Point(startX + spacing * (rightToLeft - 1) + 50, labelY1);
            subjectLabel.Size = new Size(70, 20);
            subjectLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectLabel.ForeColor = Color.FromArgb(73, 80, 87);
            subjectLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(subjectLabel);

            subjectComboBox = new ComboBox();
            subjectComboBox.Location = new Point(startX + spacing * (rightToLeft - 1), controlY1);
            subjectComboBox.Size = new Size(170, 25);
            subjectComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            subjectComboBox.BackColor = Color.White;
            subjectComboBox.ForeColor = Color.FromArgb(73, 80, 87);
            subjectComboBox.FlatStyle = FlatStyle.Flat;
            subjectComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            subjectComboBox.Items.AddRange(new string[] {
                "دراسات إسلامية", "لغة عربية", "دراسات اجتماعية", "رياضيات",
                "علوم", "لغة إنجليزية", "تربية بدنية", "تربية فنية",
                "مهارات حياتية", "تقنية رقمية"
            });
            inputGroupBox.Controls.Add(subjectComboBox);

            // Registration Number Label and TextBox (الثالث من اليمين)
            Label registrationLabel = new Label();
            registrationLabel.Text = "رقم السجل";
            registrationLabel.Location = new Point(startX + spacing * (rightToLeft - 2) + 50, labelY1);
            registrationLabel.Size = new Size(70, 20);
            registrationLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            registrationLabel.ForeColor = Color.FromArgb(73, 80, 87);
            registrationLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(registrationLabel);

            registrationNumberTextBox = new TextBox();
            registrationNumberTextBox.Location = new Point(startX + spacing * (rightToLeft - 2), controlY1);
            registrationNumberTextBox.Size = new Size(170, 25);
            registrationNumberTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            registrationNumberTextBox.BackColor = Color.White;
            registrationNumberTextBox.ForeColor = Color.FromArgb(73, 80, 87);
            registrationNumberTextBox.BorderStyle = BorderStyle.FixedSingle;
            inputGroupBox.Controls.Add(registrationNumberTextBox);

            // Name Label and TextBox (الرابع والأخير من اليمين - أقصى اليسار)
            Label nameLabel = new Label();
            nameLabel.Text = "الاسم";
            nameLabel.Location = new Point(startX + spacing * (rightToLeft - 3) + 50, labelY1);
            nameLabel.Size = new Size(70, 20);
            nameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            nameLabel.ForeColor = Color.FromArgb(73, 80, 87);
            nameLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(nameLabel);

            nameTextBox = new TextBox();
            nameTextBox.Location = new Point(startX + spacing * (rightToLeft - 3), controlY1);
            nameTextBox.Size = new Size(170, 25);
            nameTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            nameTextBox.BackColor = Color.White;
            nameTextBox.ForeColor = Color.FromArgb(73, 80, 87);
            nameTextBox.BorderStyle = BorderStyle.FixedSingle;
            inputGroupBox.Controls.Add(nameTextBox);

            // الصف الثاني من اليمين إلى اليسار: مسؤول المتابعة، رقم الجوال، عدد الحصص

            // Supervisor Label and ComboBox (الأول من اليمين في الصف الثاني)
            Label supervisorLabel = new Label();
            supervisorLabel.Text = "مسؤول المتابعة";
            supervisorLabel.Location = new Point(startX + spacing * 2 + 50, labelY2);
            supervisorLabel.Size = new Size(70, 20);
            supervisorLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorLabel.ForeColor = Color.FromArgb(73, 80, 87);
            supervisorLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(supervisorLabel);

            supervisorComboBox = new ComboBox();
            supervisorComboBox.Location = new Point(startX + spacing * 2, controlY2);
            supervisorComboBox.Size = new Size(170, 25);
            supervisorComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            supervisorComboBox.BackColor = Color.White;
            supervisorComboBox.ForeColor = Color.FromArgb(73, 80, 87);
            supervisorComboBox.FlatStyle = FlatStyle.Flat;
            supervisorComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            supervisorComboBox.Items.AddRange(new string[] {
                "أحمد محمد", "فاطمة علي", "محمد حسن", "نورا أحمد", "سارة محمود"
            });
            inputGroupBox.Controls.Add(supervisorComboBox);

            // Phone Label and TextBox (الثاني من اليمين في الصف الثاني)
            Label phoneLabel = new Label();
            phoneLabel.Text = "رقم الجوال";
            phoneLabel.Location = new Point(startX + spacing + 50, labelY2);
            phoneLabel.Size = new Size(70, 20);
            phoneLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            phoneLabel.ForeColor = Color.FromArgb(73, 80, 87);
            phoneLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(phoneLabel);

            phoneTextBox = new TextBox();
            phoneTextBox.Location = new Point(startX + spacing, controlY2);
            phoneTextBox.Size = new Size(170, 25);
            phoneTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            phoneTextBox.BackColor = Color.White;
            phoneTextBox.ForeColor = Color.FromArgb(73, 80, 87);
            phoneTextBox.BorderStyle = BorderStyle.FixedSingle;
            phoneTextBox.MaxLength = 10;
            phoneTextBox.KeyPress += PhoneTextBox_KeyPress;
            inputGroupBox.Controls.Add(phoneTextBox);

            // Class Hours Label and ComboBox (الثالث والأخير من اليمين في الصف الثاني - أقصى اليسار)
            Label classHoursLabel = new Label();
            classHoursLabel.Text = "عدد الحصص";
            classHoursLabel.Location = new Point(startX + 50, labelY2);
            classHoursLabel.Size = new Size(70, 20);
            classHoursLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            classHoursLabel.ForeColor = Color.FromArgb(73, 80, 87);
            classHoursLabel.TextAlign = ContentAlignment.MiddleCenter;
            inputGroupBox.Controls.Add(classHoursLabel);

            classHoursComboBox = new ComboBox();
            classHoursComboBox.Location = new Point(startX, controlY2);
            classHoursComboBox.Size = new Size(170, 25);
            classHoursComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            classHoursComboBox.BackColor = Color.White;
            classHoursComboBox.ForeColor = Color.FromArgb(73, 80, 87);
            classHoursComboBox.FlatStyle = FlatStyle.Flat;
            classHoursComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            for (int i = 1; i <= 24; i++)
            {
                classHoursComboBox.Items.Add(i.ToString());
            }
            inputGroupBox.Controls.Add(classHoursComboBox);
        }

        private void CreateDataGridView()
        {
            // Simple DataGridView without custom styling
            teachersDataGridView = new DataGridView();
            teachersDataGridView.Location = new Point(20, 360); // تحديث الموقع ليكون تحت مجموعة الإدخال الجديدة
            teachersDataGridView.Size = new Size(1100, 300);
            teachersDataGridView.AllowUserToAddRows = false;
            teachersDataGridView.AllowUserToDeleteRows = false;
            teachersDataGridView.ReadOnly = true;
            teachersDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            teachersDataGridView.MultiSelect = false;
            teachersDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            teachersDataGridView.SelectionChanged += TeachersDataGridView_SelectionChanged;

            // Add columns
            teachersDataGridView.Columns.Add("Id", "م");
            teachersDataGridView.Columns.Add("Name", "الاسم");
            teachersDataGridView.Columns.Add("RegistrationNumber", "رقم السجل");
            teachersDataGridView.Columns.Add("Subject", "التخصص");
            teachersDataGridView.Columns.Add("Stage", "المرحلة");
            teachersDataGridView.Columns.Add("ClassHours", "الحصص");
            teachersDataGridView.Columns.Add("PhoneNumber", "رقم الجوال");
            teachersDataGridView.Columns.Add("Supervisor", "مسؤول المتابعة");

            this.Controls.Add(teachersDataGridView);
        }

        private void CreateSearchControls()
        {
            // Search Title
            Label searchTitleLabel = new Label();
            searchTitleLabel.Text = "البحث:";
            searchTitleLabel.Location = new Point(20, 250);
            searchTitleLabel.Size = new Size(60, 23);
            searchTitleLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            searchTitleLabel.ForeColor = Color.DarkBlue;
            this.Controls.Add(searchTitleLabel);

            int searchY = 280;
            int searchSpacing = 180;
            int searchStartX = 20;

            // Search Name
            Label searchNameLabel = new Label();
            searchNameLabel.Text = "الاسم:";
            searchNameLabel.Location = new Point(searchStartX, searchY);
            searchNameLabel.Size = new Size(60, 25);
            searchNameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            this.Controls.Add(searchNameLabel);

            searchNameTextBox = new TextBox();
            searchNameTextBox.Location = new Point(searchStartX + 60, searchY);
            searchNameTextBox.Size = new Size(100, 25);
            searchNameTextBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchNameTextBox.TextChanged += SearchTextBox_TextChanged;
            this.Controls.Add(searchNameTextBox);

            // Search Subject
            Label searchSubjectLabel = new Label();
            searchSubjectLabel.Text = "التخصص:";
            searchSubjectLabel.Location = new Point(searchStartX + searchSpacing, searchY);
            searchSubjectLabel.Size = new Size(60, 25);
            searchSubjectLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            this.Controls.Add(searchSubjectLabel);

            searchSubjectComboBox = new ComboBox();
            searchSubjectComboBox.Location = new Point(searchStartX + searchSpacing + 70, searchY);
            searchSubjectComboBox.Size = new Size(100, 25);
            searchSubjectComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchSubjectComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            searchSubjectComboBox.Items.Add("الكل");
            searchSubjectComboBox.Items.AddRange(new string[] {
                "دراسات إسلامية", "لغة عربية", "دراسات اجتماعية", "رياضيات",
                "علوم", "لغة إنجليزية", "تربية بدنية", "تربية فنية",
                "مهارات حياتية", "تقنية رقمية"
            });
            searchSubjectComboBox.SelectedIndex = 0;
            searchSubjectComboBox.SelectedIndexChanged += SearchComboBox_SelectedIndexChanged;
            this.Controls.Add(searchSubjectComboBox);

            // Search Stage
            Label searchStageLabel = new Label();
            searchStageLabel.Text = "المرحلة:";
            searchStageLabel.Location = new Point(searchStartX + searchSpacing * 2, searchY);
            searchStageLabel.Size = new Size(60, 25);
            searchStageLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            this.Controls.Add(searchStageLabel);

            searchStageComboBox = new ComboBox();
            searchStageComboBox.Location = new Point(searchStartX + searchSpacing * 2 + 70, searchY);
            searchStageComboBox.Size = new Size(100, 25);
            searchStageComboBox.Font = new Font("Arial", 12, FontStyle.Bold);
            searchStageComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            searchStageComboBox.Items.Add("الكل");
            searchStageComboBox.Items.AddRange(new string[] {
                "ابتدائي", "متوسط", "ثانوي", "تربية خاصة"
            });
            searchStageComboBox.SelectedIndex = 0;
            searchStageComboBox.SelectedIndexChanged += SearchComboBox_SelectedIndexChanged;
            this.Controls.Add(searchStageComboBox);

            // Clear Search Button
            clearSearchButton = new Button();
            clearSearchButton.Text = "مسح البحث";
            clearSearchButton.Location = new Point(searchStartX + searchSpacing * 3, searchY - 2);
            clearSearchButton.Size = new Size(100, 30);
            clearSearchButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearSearchButton.BackColor = Color.LightGray;
            clearSearchButton.FlatStyle = FlatStyle.Flat;
            clearSearchButton.Click += ClearSearchButton_Click;
            this.Controls.Add(clearSearchButton);
        }

        private void CreateButtons()
        {
            // Buttons GroupBox - CSS-like styling
            buttonsGroupBox = new GroupBox();
            buttonsGroupBox.Text = "العمليات";
            buttonsGroupBox.Location = new Point(100, 150);
            buttonsGroupBox.Size = new Size(800, 80);
            buttonsGroupBox.Font = new Font("Arial", 12, FontStyle.Bold);
            buttonsGroupBox.RightToLeft = RightToLeft.Yes;
            buttonsGroupBox.ForeColor = Color.DarkBlue;
            buttonsGroupBox.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            buttonsGroupBox.FlatStyle = FlatStyle.Flat;
            this.Controls.Add(buttonsGroupBox);

            // CSS-like button container calculations
            int buttonWidth = 100;
            int buttonHeight = 35;
            int buttonSpacing = 20; // Gap between buttons
            int totalButtonsWidth = (buttonWidth * 5) + (buttonSpacing * 4); // 5 buttons + 4 gaps
            int containerWidth = buttonsGroupBox.Width - 40; // Container padding
            int startX = (containerWidth - totalButtonsWidth) / 2 + 20; // Center horizontally
            int buttonY = (buttonsGroupBox.Height - buttonHeight) / 2 - 5; // Perfect vertical center

            // CSS-like button styling with modern design - ترتيب من اليمين
            // حساب المواقع من اليمين إلى اليسار
            int rightMostButtonX = startX + (buttonWidth + buttonSpacing) * 4; // أقصى اليمين

            // Save Button (الأول من اليمين)
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Location = new Point(rightMostButtonX, buttonY);
            saveButton.Size = new Size(buttonWidth, buttonHeight);
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(34, 142, 58);
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            buttonsGroupBox.Controls.Add(saveButton);

            // Edit Button (الثاني من اليمين)
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Location = new Point(rightMostButtonX - (buttonWidth + buttonSpacing), buttonY);
            editButton.Size = new Size(buttonWidth, buttonHeight);
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(0, 105, 217);
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            editButton.Enabled = false;
            buttonsGroupBox.Controls.Add(editButton);

            // Delete Button (الثالث من اليمين)
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Location = new Point(rightMostButtonX - (buttonWidth + buttonSpacing) * 2, buttonY);
            deleteButton.Size = new Size(buttonWidth, buttonHeight);
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(200, 35, 51);
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            deleteButton.Enabled = false;
            buttonsGroupBox.Controls.Add(deleteButton);

            // Clear Button (الرابع من اليمين)
            clearButton = new Button();
            clearButton.Text = "مسح الحقول";
            clearButton.Location = new Point(rightMostButtonX - (buttonWidth + buttonSpacing) * 3, buttonY);
            clearButton.Size = new Size(buttonWidth, buttonHeight);
            clearButton.Font = new Font("Arial", 12, FontStyle.Bold);
            clearButton.BackColor = Color.FromArgb(255, 193, 7); // Bootstrap warning yellow
            clearButton.ForeColor = Color.Black;
            clearButton.FlatStyle = FlatStyle.Flat;
            clearButton.FlatAppearance.BorderSize = 0;
            clearButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 173, 0);
            clearButton.Cursor = Cursors.Hand;
            clearButton.Click += ClearButton_Click;
            buttonsGroupBox.Controls.Add(clearButton);

            // Report Button (الخامس من اليمين - الأخير)
            reportButton = new Button();
            reportButton.Text = "تقرير";
            reportButton.Location = new Point(rightMostButtonX - (buttonWidth + buttonSpacing) * 4, buttonY);
            reportButton.Size = new Size(buttonWidth, buttonHeight);
            reportButton.Font = new Font("Arial", 12, FontStyle.Bold);
            reportButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            reportButton.ForeColor = Color.White;
            reportButton.FlatStyle = FlatStyle.Flat;
            reportButton.FlatAppearance.BorderSize = 0;
            reportButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(90, 98, 104);
            reportButton.Cursor = Cursors.Hand;
            reportButton.Click += ReportButton_Click;
            buttonsGroupBox.Controls.Add(reportButton);
        }

        private void CreateCountLabel()
        {
            teacherCountLabel = new Label();
            teacherCountLabel.Text = "عدد المعلمين: 0";
            teacherCountLabel.Location = new Point(20, 630);
            teacherCountLabel.Size = new Size(200, 25);
            teacherCountLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            teacherCountLabel.ForeColor = Color.DarkBlue;
            this.Controls.Add(teacherCountLabel);
        }

        private void LoadTeachersFromFile()
        {
            try
            {
                if (File.Exists(teachersFilePath))
                {
                    string[] lines = File.ReadAllLines(teachersFilePath);
                    foreach (string line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            string[] parts = line.Split('|');
                            if (parts.Length >= 8) // جميع البيانات المطلوبة مع مسؤول المتابعة
                            {
                                var teacher = new Teacher
                                {
                                    Id = int.Parse(parts[0]),
                                    Name = parts[1],
                                    RegistrationNumber = parts[2],
                                    Subject = parts[3],
                                    Stage = parts[4],
                                    ClassHours = int.Parse(parts[5]),
                                    PhoneNumber = parts[6],
                                    Supervisor = parts.Length > 7 ? parts[7] : "" // مسؤول المتابعة (اختياري للملفات القديمة)
                                };
                                teachers.Add(teacher);

                                // تحديث nextId ليكون أكبر من أكبر ID موجود
                                if (teacher.Id >= nextId)
                                {
                                    nextId = teacher.Id + 1;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل بيانات المعلمين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveTeachersToFile()
        {
            try
            {
                // إنشاء المجلد إذا لم يكن موجوداً
                string directory = Path.GetDirectoryName(teachersFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // حفظ بيانات المعلمين في الملف
                var lines = new List<string>();
                foreach (var teacher in teachers)
                {
                    string line = $"{teacher.Id}|{teacher.Name}|{teacher.RegistrationNumber}|{teacher.Subject}|{teacher.Stage}|{teacher.ClassHours}|{teacher.PhoneNumber}|{teacher.Supervisor}";
                    lines.Add(line);
                }

                File.WriteAllLines(teachersFilePath, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ بيانات المعلمين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGrid()
        {
            teachersDataGridView.Rows.Clear();

            var teachersToShow = filteredTeachers.Count > 0 || !string.IsNullOrEmpty(searchNameTextBox?.Text) ||
                               (searchSubjectComboBox?.SelectedIndex > 0) || (searchStageComboBox?.SelectedIndex > 0)
                               ? filteredTeachers : teachers;

            foreach (var teacher in teachersToShow)
            {
                teachersDataGridView.Rows.Add(teacher.Id, teacher.Name, teacher.RegistrationNumber,
                                            teacher.Subject, teacher.Stage, teacher.ClassHours, teacher.PhoneNumber, teacher.Supervisor);
            }
        }

        private void UpdateTeacherCount()
        {
            teacherCountLabel.Text = $"عدد المعلمين: {teachers.Count}";
        }

        private void PhoneTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Allow only digits and control characters
            if (!char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar))
            {
                e.Handled = true;
            }
        }



        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                if (selectedTeacherId == -1)
                {
                    // Add new teacher
                    var teacher = new Teacher
                    {
                        Id = nextId++,
                        Name = nameTextBox.Text.Trim(),
                        RegistrationNumber = registrationNumberTextBox.Text.Trim(),
                        Subject = subjectComboBox.Text,
                        Stage = stageComboBox.Text,
                        ClassHours = int.Parse(classHoursComboBox.Text),
                        PhoneNumber = phoneTextBox.Text.Trim(),
                        Supervisor = supervisorComboBox.Text
                    };
                    teachers.Add(teacher);

                    // حفظ المعلمين في الملف
                    SaveTeachersToFile();

                    // إضافة المعلم الجديد إلى نافذة التوزيع الآلي مع التوزيع التلقائي
                    if (autoDistributionForm != null)
                    {
                        autoDistributionForm.AddNewTeacherWithAutoDistribution(teacher);
                    }

                    MessageBox.Show("تم حفظ بيانات المعلم بنجاح وإضافته إلى التوزيع الآلي!", "تأكيد الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Update existing teacher
                    var teacher = teachers.FirstOrDefault(t => t.Id == selectedTeacherId);
                    if (teacher != null)
                    {
                        teacher.Name = nameTextBox.Text.Trim();
                        teacher.RegistrationNumber = registrationNumberTextBox.Text.Trim();
                        teacher.Subject = subjectComboBox.Text;
                        teacher.Stage = stageComboBox.Text;
                        teacher.ClassHours = int.Parse(classHoursComboBox.Text);
                        teacher.PhoneNumber = phoneTextBox.Text.Trim();
                        teacher.Supervisor = supervisorComboBox.Text;

                        // حفظ المعلمين في الملف
                        SaveTeachersToFile();

                        // تحديث بيانات المعلم في نافذة التوزيع الآلي
                        if (autoDistributionForm != null)
                        {
                            autoDistributionForm.UpdateTeacherData(teacher);
                        }

                        MessageBox.Show("تم تعديل بيانات المعلم بنجاح!", "تأكيد التعديل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }

                ClearInputFields();
                RefreshDataGrid();
                UpdateTeacherCount();
                ApplySearch();
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (teachersDataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = teachersDataGridView.SelectedRows[0];
                selectedTeacherId = (int)selectedRow.Cells["Id"].Value;

                var teacher = teachers.FirstOrDefault(t => t.Id == selectedTeacherId);
                if (teacher != null)
                {
                    nameTextBox.Text = teacher.Name;
                    registrationNumberTextBox.Text = teacher.RegistrationNumber;
                    subjectComboBox.Text = teacher.Subject;
                    stageComboBox.Text = teacher.Stage;
                    classHoursComboBox.Text = teacher.ClassHours.ToString();
                    phoneTextBox.Text = teacher.PhoneNumber;
                    supervisorComboBox.Text = teacher.Supervisor;

                    saveButton.Text = "تحديث";
                }
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (teachersDataGridView.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذا المعلم؟", "تأكيد الحذف",
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var selectedRow = teachersDataGridView.SelectedRows[0];
                    int teacherId = (int)selectedRow.Cells["Id"].Value;

                    var teacher = teachers.FirstOrDefault(t => t.Id == teacherId);
                    if (teacher != null)
                    {
                        teachers.Remove(teacher);

                        // حفظ المعلمين في الملف بعد الحذف
                        SaveTeachersToFile();

                        MessageBox.Show("تم حذف المعلم بنجاح!", "تأكيد الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        ClearInputFields();
                        RefreshDataGrid();
                        UpdateTeacherCount();
                        ApplySearch();
                    }
                }
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            ClearInputFields();
        }

        private void ReportButton_Click(object sender, EventArgs e)
        {
            GenerateReport();
        }

        private void ClearInputFields()
        {
            nameTextBox.Clear();
            registrationNumberTextBox.Clear();
            subjectComboBox.SelectedIndex = -1;
            stageComboBox.SelectedIndex = -1;
            classHoursComboBox.SelectedIndex = -1;
            phoneTextBox.Clear();
            supervisorComboBox.SelectedIndex = -1;
            selectedTeacherId = -1;
            saveButton.Text = "حفظ";
            editButton.Enabled = false;
            deleteButton.Enabled = false;
        }

        private void TeachersDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (teachersDataGridView.SelectedRows.Count > 0)
            {
                editButton.Enabled = true;
                deleteButton.Enabled = true;
            }
            else
            {
                editButton.Enabled = false;
                deleteButton.Enabled = false;
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            ApplySearch();
        }

        private void SearchComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplySearch();
        }

        private void ClearSearchButton_Click(object sender, EventArgs e)
        {
            searchNameTextBox.Clear();
            searchSubjectComboBox.SelectedIndex = 0;
            searchStageComboBox.SelectedIndex = 0;
            ApplySearch();
        }

        private void ApplySearch()
        {
            filteredTeachers.Clear();

            var searchName = searchNameTextBox?.Text?.Trim().ToLower() ?? "";
            var searchSubject = searchSubjectComboBox?.SelectedIndex > 0 ? searchSubjectComboBox.Text : "";
            var searchStage = searchStageComboBox?.SelectedIndex > 0 ? searchStageComboBox.Text : "";

            if (string.IsNullOrEmpty(searchName) && string.IsNullOrEmpty(searchSubject) && string.IsNullOrEmpty(searchStage))
            {
                filteredTeachers.Clear();
            }
            else
            {
                filteredTeachers = teachers.Where(t =>
                    (string.IsNullOrEmpty(searchName) || t.Name.ToLower().Contains(searchName)) &&
                    (string.IsNullOrEmpty(searchSubject) || t.Subject == searchSubject) &&
                    (string.IsNullOrEmpty(searchStage) || t.Stage == searchStage)
                ).ToList();
            }

            RefreshDataGrid();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المعلم", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(registrationNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم السجل", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                registrationNumberTextBox.Focus();
                return false;
            }

            if (subjectComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار التخصص", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                subjectComboBox.Focus();
                return false;
            }

            if (stageComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار المرحلة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                stageComboBox.Focus();
                return false;
            }

            if (classHoursComboBox.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار عدد الحصص", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                classHoursComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(phoneTextBox.Text) || phoneTextBox.Text.Length != 10)
            {
                MessageBox.Show("يرجى إدخال رقم جوال صحيح (10 أرقام بالضبط)", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                phoneTextBox.Focus();
                return false;
            }

            // Check for duplicate registration number (except when editing the same teacher)
            var existingTeacherByReg = teachers.FirstOrDefault(t => t.RegistrationNumber == registrationNumberTextBox.Text.Trim() && t.Id != selectedTeacherId);
            if (existingTeacherByReg != null)
            {
                MessageBox.Show("رقم السجل مسجل مسبقاً لمعلم آخر", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                registrationNumberTextBox.Focus();
                return false;
            }

            // Check for duplicate phone number (except when editing the same teacher)
            var existingTeacherByPhone = teachers.FirstOrDefault(t => t.PhoneNumber == phoneTextBox.Text.Trim() && t.Id != selectedTeacherId);
            if (existingTeacherByPhone != null)
            {
                MessageBox.Show("رقم الجوال مسجل مسبقاً لمعلم آخر", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                phoneTextBox.Focus();
                return false;
            }

            return true;
        }

        private void GenerateReport()
        {
            try
            {
                // إنشاء محتوى التقرير
                var reportContent = GenerateReportContent();

                // إنشاء نافذة التقرير
                var reportForm = new ReportForm(reportContent);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateReportContent()
        {
            var reportBuilder = new System.Text.StringBuilder();

            // عنوان التقرير
            reportBuilder.AppendLine("=".PadLeft(80, '='));
            reportBuilder.AppendLine("تقرير بيانات المعلمين".PadLeft(50));
            reportBuilder.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}".PadLeft(55));
            reportBuilder.AppendLine("=".PadLeft(80, '='));
            reportBuilder.AppendLine();

            // إحصائيات عامة
            reportBuilder.AppendLine("الإحصائيات العامة:");
            reportBuilder.AppendLine($"إجمالي عدد المعلمين: {teachers.Count}");

            // إحصائيات حسب التخصص
            var subjectStats = teachers.GroupBy(t => t.Subject)
                                     .Select(g => new { Subject = g.Key, Count = g.Count() })
                                     .OrderByDescending(x => x.Count);

            reportBuilder.AppendLine("\nتوزيع المعلمين حسب التخصص:");
            foreach (var stat in subjectStats)
            {
                reportBuilder.AppendLine($"  - {stat.Subject}: {stat.Count} معلم");
            }

            // إحصائيات حسب المرحلة
            var stageStats = teachers.GroupBy(t => t.Stage)
                                   .Select(g => new { Stage = g.Key, Count = g.Count() })
                                   .OrderByDescending(x => x.Count);

            reportBuilder.AppendLine("\nتوزيع المعلمين حسب المرحلة:");
            foreach (var stat in stageStats)
            {
                reportBuilder.AppendLine($"  - {stat.Stage}: {stat.Count} معلم");
            }

            // إحصائيات الحصص
            var totalHours = teachers.Sum(t => t.ClassHours);
            var avgHours = teachers.Count > 0 ? teachers.Average(t => t.ClassHours) : 0;

            reportBuilder.AppendLine($"\nإحصائيات الحصص:");
            reportBuilder.AppendLine($"إجمالي عدد الحصص: {totalHours} حصة");
            reportBuilder.AppendLine($"متوسط الحصص لكل معلم: {avgHours:F1} حصة");

            reportBuilder.AppendLine();
            reportBuilder.AppendLine("=".PadLeft(80, '='));
            reportBuilder.AppendLine("تفاصيل بيانات المعلمين:");
            reportBuilder.AppendLine("=".PadLeft(80, '='));

            // عناوين الأعمدة
            reportBuilder.AppendLine();
            reportBuilder.AppendLine($"{"م",-5} {"الاسم",-20} {"رقم السجل",-12} {"التخصص",-18} {"المرحلة",-12} {"الحصص",-8} {"رقم الجوال",-12}");
            reportBuilder.AppendLine("-".PadLeft(90, '-'));

            // بيانات المعلمين
            var teachersToShow = filteredTeachers.Count > 0 || !string.IsNullOrEmpty(searchNameTextBox?.Text) ||
                               (searchSubjectComboBox?.SelectedIndex > 0) || (searchStageComboBox?.SelectedIndex > 0)
                               ? filteredTeachers : teachers;

            int counter = 1;
            foreach (var teacher in teachersToShow)
            {
                reportBuilder.AppendLine($"{counter,-5} {teacher.Name,-20} {teacher.RegistrationNumber,-12} " +
                                       $"{teacher.Subject,-18} {teacher.Stage,-12} {teacher.ClassHours,-8} {teacher.PhoneNumber,-12}");
                counter++;
            }

            reportBuilder.AppendLine();
            reportBuilder.AppendLine("=".PadLeft(80, '='));
            reportBuilder.AppendLine($"انتهى التقرير - إجمالي السجلات المعروضة: {teachersToShow.Count()}");
            reportBuilder.AppendLine("=".PadLeft(80, '='));

            return reportBuilder.ToString();
        }
    }
}
