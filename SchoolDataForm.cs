using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class SchoolDataForm : Form
    {
        private TextBox countryTextBox;
        private TextBox ministryTextBox;
        private TextBox educationDepartmentTextBox;
        private TextBox schoolNameTextBox;
        private TextBox principalNameTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;

        private readonly string schoolDataFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "SchoolData.txt");

        public SchoolDataForm()
        {
            InitializeComponent();
            LoadSchoolData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "بيانات المدرسة";
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "بيانات المدرسة";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(73, 80, 87);
            titleLabel.Size = new Size(400, 40);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.BackColor = Color.White;
            titleLabel.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(titleLabel);

            // Country
            Label countryLabel = new Label();
            countryLabel.Text = "الدولة:";
            countryLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            countryLabel.Size = new Size(100, 25);
            countryLabel.Location = new Point(50, 80);
            this.Controls.Add(countryLabel);

            countryTextBox = new TextBox();
            countryTextBox.Font = new Font("Arial", 12);
            countryTextBox.Size = new Size(300, 25);
            countryTextBox.Location = new Point(150, 80);
            this.Controls.Add(countryTextBox);

            // Ministry
            Label ministryLabel = new Label();
            ministryLabel.Text = "الوزارة:";
            ministryLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            ministryLabel.Size = new Size(100, 25);
            ministryLabel.Location = new Point(50, 120);
            this.Controls.Add(ministryLabel);

            ministryTextBox = new TextBox();
            ministryTextBox.Font = new Font("Arial", 12);
            ministryTextBox.Size = new Size(300, 25);
            ministryTextBox.Location = new Point(150, 120);
            this.Controls.Add(ministryTextBox);

            // Education Department
            Label educationDepartmentLabel = new Label();
            educationDepartmentLabel.Text = "إدارة التعليم:";
            educationDepartmentLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            educationDepartmentLabel.Size = new Size(100, 25);
            educationDepartmentLabel.Location = new Point(50, 160);
            this.Controls.Add(educationDepartmentLabel);

            educationDepartmentTextBox = new TextBox();
            educationDepartmentTextBox.Font = new Font("Arial", 12);
            educationDepartmentTextBox.Size = new Size(300, 25);
            educationDepartmentTextBox.Location = new Point(150, 160);
            this.Controls.Add(educationDepartmentTextBox);

            // School Name
            Label schoolNameLabel = new Label();
            schoolNameLabel.Text = "اسم المدرسة:";
            schoolNameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            schoolNameLabel.Size = new Size(100, 25);
            schoolNameLabel.Location = new Point(50, 200);
            this.Controls.Add(schoolNameLabel);

            schoolNameTextBox = new TextBox();
            schoolNameTextBox.Font = new Font("Arial", 12);
            schoolNameTextBox.Size = new Size(300, 25);
            schoolNameTextBox.Location = new Point(150, 200);
            this.Controls.Add(schoolNameTextBox);

            // Principal Name
            Label principalNameLabel = new Label();
            principalNameLabel.Text = "اسم المدير:";
            principalNameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            principalNameLabel.Size = new Size(100, 25);
            principalNameLabel.Location = new Point(50, 240);
            this.Controls.Add(principalNameLabel);

            principalNameTextBox = new TextBox();
            principalNameTextBox.Font = new Font("Arial", 12);
            principalNameTextBox.Size = new Size(300, 25);
            principalNameTextBox.Location = new Point(150, 240);
            this.Controls.Add(principalNameTextBox);

            // Buttons - First Row
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(100, 40);
            saveButton.Location = new Point(100, 300);
            saveButton.BackColor = Color.FromArgb(40, 167, 69);
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(100, 40);
            editButton.Location = new Point(250, 300);
            editButton.BackColor = Color.FromArgb(255, 193, 7);
            editButton.ForeColor = Color.Black;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Buttons - Second Row
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(100, 40);
            deleteButton.Location = new Point(100, 360);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69);
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            closeButton = new Button();
            closeButton.Text = "إغلاق";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(100, 40);
            closeButton.Location = new Point(250, 360);
            closeButton.BackColor = Color.FromArgb(108, 117, 125);
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);

            this.ResumeLayout(false);
        }

        private void LoadSchoolData()
        {
            try
            {
                if (File.Exists(schoolDataFilePath))
                {
                    string[] lines = File.ReadAllLines(schoolDataFilePath);
                    if (lines.Length >= 5)
                    {
                        countryTextBox.Text = lines[0];
                        ministryTextBox.Text = lines[1];
                        educationDepartmentTextBox.Text = lines[2];
                        schoolNameTextBox.Text = lines[3];
                        principalNameTextBox.Text = lines[4];
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المدرسة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(schoolDataFilePath);
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                string[] data = {
                    countryTextBox.Text,
                    ministryTextBox.Text,
                    educationDepartmentTextBox.Text,
                    schoolNameTextBox.Text,
                    principalNameTextBox.Text
                };

                File.WriteAllLines(schoolDataFilePath, data);
                MessageBox.Show("تم حفظ بيانات المدرسة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات المدرسة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            // Enable editing
            countryTextBox.ReadOnly = false;
            ministryTextBox.ReadOnly = false;
            educationDepartmentTextBox.ReadOnly = false;
            schoolNameTextBox.ReadOnly = false;
            principalNameTextBox.ReadOnly = false;
            MessageBox.Show("يمكنك الآن تعديل البيانات", "تعديل", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من حذف جميع بيانات المدرسة؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                countryTextBox.Clear();
                ministryTextBox.Clear();
                educationDepartmentTextBox.Clear();
                schoolNameTextBox.Clear();
                principalNameTextBox.Clear();
                
                if (File.Exists(schoolDataFilePath))
                {
                    File.Delete(schoolDataFilePath);
                }
                
                MessageBox.Show("تم حذف بيانات المدرسة!", "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
