using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace SchoolManagement
{
    public partial class SchoolDataForm : Form
    {
        // مسار ملف حفظ بيانات المدرسة
        private readonly string schoolDataFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SchoolManagement", "SchoolData.txt");

        // Controls
        private TextBox countryTextBox;
        private TextBox ministryTextBox;
        private TextBox educationDepartmentTextBox;
        private TextBox schoolNameTextBox;
        private TextBox principalNameTextBox;
        private Button saveButton;
        private Button editButton;
        private Button deleteButton;
        private Button closeButton;

        public SchoolDataForm()
        {
            InitializeComponent();
            LoadSchoolData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties - CSS-like modern design
            this.Text = "بيانات المدرسة";
            this.Size = new Size(400, 520);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250); // Light gray background
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            Label titleLabel = new Label();
            titleLabel.Text = "بيانات المدرسة";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 58, 64);
            titleLabel.Size = new Size(300, 30);
            titleLabel.Location = new Point(50, 20);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // Country Label and TextBox
            Label countryLabel = new Label();
            countryLabel.Text = "الدولة";
            countryLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            countryLabel.Size = new Size(100, 25);
            countryLabel.Location = new Point(50, 70);
            this.Controls.Add(countryLabel);

            countryTextBox = new TextBox();
            countryTextBox.Font = new Font("Arial", 12);
            countryTextBox.Size = new Size(300, 30);
            countryTextBox.Location = new Point(50, 100);
            this.Controls.Add(countryTextBox);

            // Ministry Label and TextBox
            Label ministryLabel = new Label();
            ministryLabel.Text = "الوزارة";
            ministryLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            ministryLabel.Size = new Size(100, 25);
            ministryLabel.Location = new Point(50, 140);
            this.Controls.Add(ministryLabel);

            ministryTextBox = new TextBox();
            ministryTextBox.Font = new Font("Arial", 12);
            ministryTextBox.Size = new Size(300, 30);
            ministryTextBox.Location = new Point(50, 170);
            this.Controls.Add(ministryTextBox);

            // Education Department Label and TextBox
            Label educationDepartmentLabel = new Label();
            educationDepartmentLabel.Text = "إدارة التعليم";
            educationDepartmentLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            educationDepartmentLabel.Size = new Size(100, 25);
            educationDepartmentLabel.Location = new Point(50, 210);
            this.Controls.Add(educationDepartmentLabel);

            educationDepartmentTextBox = new TextBox();
            educationDepartmentTextBox.Font = new Font("Arial", 12);
            educationDepartmentTextBox.Size = new Size(300, 30);
            educationDepartmentTextBox.Location = new Point(50, 240);
            this.Controls.Add(educationDepartmentTextBox);

            // School Name Label and TextBox
            Label schoolNameLabel = new Label();
            schoolNameLabel.Text = "اسم المدرسة";
            schoolNameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            schoolNameLabel.Size = new Size(100, 25);
            schoolNameLabel.Location = new Point(50, 280);
            this.Controls.Add(schoolNameLabel);

            schoolNameTextBox = new TextBox();
            schoolNameTextBox.Font = new Font("Arial", 12);
            schoolNameTextBox.Size = new Size(300, 30);
            schoolNameTextBox.Location = new Point(50, 310);
            this.Controls.Add(schoolNameTextBox);

            // Principal Name Label and TextBox
            Label principalNameLabel = new Label();
            principalNameLabel.Text = "اسم مدير المدرسة";
            principalNameLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            principalNameLabel.Size = new Size(150, 25);
            principalNameLabel.Location = new Point(50, 350);
            this.Controls.Add(principalNameLabel);

            principalNameTextBox = new TextBox();
            principalNameTextBox.Font = new Font("Arial", 12);
            principalNameTextBox.Size = new Size(300, 30);
            principalNameTextBox.Location = new Point(50, 380);
            this.Controls.Add(principalNameTextBox);

            // Buttons
            CreateButtons();

            this.ResumeLayout(false);
        }

        private void CreateButtons()
        {
            // Save Button (أقصى اليسار)
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Font = new Font("Arial", 12, FontStyle.Bold);
            saveButton.Size = new Size(70, 35);
            saveButton.Location = new Point(40, 440);
            saveButton.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap success green
            saveButton.ForeColor = Color.White;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Cursor = Cursors.Hand;
            saveButton.Click += SaveButton_Click;
            this.Controls.Add(saveButton);

            // Edit Button (ثاني من اليسار)
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Font = new Font("Arial", 12, FontStyle.Bold);
            editButton.Size = new Size(70, 35);
            editButton.Location = new Point(120, 440);
            editButton.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap primary blue
            editButton.ForeColor = Color.White;
            editButton.FlatStyle = FlatStyle.Flat;
            editButton.FlatAppearance.BorderSize = 0;
            editButton.Cursor = Cursors.Hand;
            editButton.Click += EditButton_Click;
            this.Controls.Add(editButton);

            // Delete Button (ثالث من اليسار)
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Font = new Font("Arial", 12, FontStyle.Bold);
            deleteButton.Size = new Size(70, 35);
            deleteButton.Location = new Point(200, 440);
            deleteButton.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap danger red
            deleteButton.ForeColor = Color.White;
            deleteButton.FlatStyle = FlatStyle.Flat;
            deleteButton.FlatAppearance.BorderSize = 0;
            deleteButton.Cursor = Cursors.Hand;
            deleteButton.Click += DeleteButton_Click;
            this.Controls.Add(deleteButton);

            // Close Button (أقصى اليمين)
            closeButton = new Button();
            closeButton.Text = "إغلاق";
            closeButton.Font = new Font("Arial", 12, FontStyle.Bold);
            closeButton.Size = new Size(70, 35);
            closeButton.Location = new Point(280, 440);
            closeButton.BackColor = Color.FromArgb(108, 117, 125); // Bootstrap secondary gray
            closeButton.ForeColor = Color.White;
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Cursor = Cursors.Hand;
            closeButton.Click += CloseButton_Click;
            this.Controls.Add(closeButton);
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveSchoolData();
                MessageBox.Show("تم حفظ بيانات المدرسة بنجاح!", "تأكيد الحفظ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveSchoolData();
                MessageBox.Show("تم تعديل بيانات المدرسة بنجاح!", "تأكيد التعديل", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من حذف جميع بيانات المدرسة؟", "تأكيد الحذف", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (result == DialogResult.Yes)
            {
                ClearAllFields();
                DeleteSchoolData();
                MessageBox.Show("تم حذف بيانات المدرسة بنجاح!", "تأكيد الحذف", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(countryTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الدولة", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                countryTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ministryTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الوزارة", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                ministryTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(educationDepartmentTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم إدارة التعليم", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                educationDepartmentTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(schoolNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المدرسة", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                schoolNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(principalNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم مدير المدرسة", "خطأ في البيانات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                principalNameTextBox.Focus();
                return false;
            }

            return true;
        }

        private void SaveSchoolData()
        {
            try
            {
                // إنشاء المجلد إذا لم يكن موجوداً
                string directory = Path.GetDirectoryName(schoolDataFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // حفظ بيانات المدرسة في الملف
                string data = $"{countryTextBox.Text.Trim()}|{ministryTextBox.Text.Trim()}|{educationDepartmentTextBox.Text.Trim()}|{schoolNameTextBox.Text.Trim()}|{principalNameTextBox.Text.Trim()}";
                File.WriteAllText(schoolDataFilePath, data);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حفظ بيانات المدرسة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSchoolData()
        {
            try
            {
                if (File.Exists(schoolDataFilePath))
                {
                    string data = File.ReadAllText(schoolDataFilePath);
                    if (!string.IsNullOrWhiteSpace(data))
                    {
                        string[] parts = data.Split('|');
                        if (parts.Length >= 5)
                        {
                            countryTextBox.Text = parts[0];
                            ministryTextBox.Text = parts[1];
                            educationDepartmentTextBox.Text = parts[2];
                            schoolNameTextBox.Text = parts[3];
                            principalNameTextBox.Text = parts[4];
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر تحميل بيانات المدرسة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSchoolData()
        {
            try
            {
                if (File.Exists(schoolDataFilePath))
                {
                    File.Delete(schoolDataFilePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تعذر حذف بيانات المدرسة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearAllFields()
        {
            countryTextBox.Clear();
            ministryTextBox.Clear();
            educationDepartmentTextBox.Clear();
            schoolNameTextBox.Clear();
            principalNameTextBox.Clear();
        }
    }
}
